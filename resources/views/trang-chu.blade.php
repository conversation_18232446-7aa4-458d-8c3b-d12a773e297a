<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="{{asset('./assets/wiki/images/favicon.ico')}}" type="image/x-icon" rel="shortcut icon" />
    <title>Tam <PERSON> Legend</title>
    <meta name="author" content="VTC" />
    <meta name="description" content="Game Chiến Thuật Hành Động: Ban-<PERSON> Đỉnh <PERSON>, Tự <PERSON> Tướng">
    <meta name="keywords" content="tam quoc,3Q,chien thuat,nhap vai,gacha,huyen thoai,3d,game hay vtc,vtc,vng,funtap,soha, hanh đong,RPG,SLG">
    <meta property="og:site_name" content="Tam Quốc Legend"/>
    <meta property="og:type" content="website"/>
    <meta property="og:url" content="https://tamquoclegend.splay.vn"/>
    <meta property="og:title" content="Tam <PERSON>"/>
    <meta property="og:description" content="Vô H<PERSON>, <PERSON><PERSON>c Lợi Khủng, Game Chiến Thuật Hành Động: Ban-Pick Đỉnh Cao, Tự Do Khiển Tướng"/>
    <meta property="og:image:type" content="image/jpeg"/>
    <meta property="og:image" content="https://tamquoclegend.splay.vn/sharefb.jpg')}}"/>

    <link rel="stylesheet" href="{{asset('./assets/wiki/css/main.css')}}"/>
    <link href="{{asset('./assets/wiki/css/0.prod.css')}}" rel="stylesheet">
    <link href="{{asset('./assets/wiki/css/prod.css')}}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css')}}">
</head>
<body class="">
<div id="wrapper" class="wrapper wrapper--homepage scaleDesktop scaleMobile">
    <div id="wrapperContent" class="wrapper__content">

        <section id="" class="section hvvh_mainsite_main_General scrollFrame" data-block-id="hvvh_mainsite_main_General">
            <div class="section__background">
                <span class="scrollwatch-pin"></span>
            </div>
            <div class="section__content">
                <div class="inner">
                    <div class="container flex space-between">
                        <div class="main-content">
                            <div class="tab-general">
                                <div class="tab1 f-SVN-Helvetica-Neue-Bold">
                                    <button class="tablinks" onclick="openCity(event, 'Nguy')" id="btn-nguy">NGỤY</button>
                                    <button class="tablinks" onclick="openCity(event, 'Thuc')" id="btn-thuc">THỤC</button>
                                    <button class="tablinks" onclick="openCity(event, 'Ngo')" id="btn-ngo">NGÔ</button>
                                    <button class="tablinks" onclick="openCity(event, 'Quan')" id="btn-quan">QUẦN</button>
                                </div>
                                <div id="Nguy" class="tabcontent1">
                                    <img class="desktop" src="{{asset('./assets/wiki/images/bg-nguy.jpg')}}" alt="Nước Ngụy">
                                    <img class="mobile" src="{{asset('./assets/wiki/images/bg-nguy-m.jpg')}}" alt="Nước Ngụy">
                                    <div class="general_card">
                                        <div class="title f-SVN-Helvetica-Neue-Bold">Tổ đội 3 tướng Ngụy, Liên Minh Nước Ngụy, Toàn bộ ST tăng 4%</div>
                                        <ul>
                                            @foreach($nguy as $k => $item)
                                                <li><a href="{{route('general_detail', $item->domain_name)}}"><img src="{{$item->image}}" alt="{{$item->name}}"></a></li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                                <div id="Thuc" class="tabcontent1">
                                    <img class="desktop" src="{{asset('./assets/wiki/images/bg-thuc.jpg')}}" alt="Nước Thục">
                                    <img class="mobile" src="{{asset('./assets/wiki/images/bg-thuc-m.jpg')}}" alt="Nước Thục">
                                    <div class="general_card">
                                        <div class="title f-SVN-Helvetica-Neue-Bold">Tổ đội 3 tướng Thục - Liên Minh Nước Thục - Thể Lực tăng 8%</div>
                                        <ul>
                                            @foreach($thuc as $k => $item)
                                                <li><a href="{{route('general_detail', $item->domain_name)}}"><img src="{{$item->image}}" alt="{{$item->name}}"></a></li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                                <div id="Ngo" class="tabcontent1">
                                    <img class="desktop" src="{{asset('./assets/wiki/images/bg-ngo.jpg')}}" alt="Nước Ngô">
                                    <img class="mobile" src="{{asset('./assets/wiki/images/bg-ngo-m.jpg')}}" alt="Nước Ngô">
                                    <div class="general_card">
                                        <div class="title f-SVN-Helvetica-Neue-Bold">Tổ đội 3 tướng Ngô - Liên Minh Nước Ngô - Tỷ lệ Bạo tăng 5%</div>
                                        <ul>
                                            @foreach($ngo as $k => $item)
                                                <li><a href="{{route('general_detail', $item->domain_name)}}"><img src="{{$item->image}}" alt="{{$item->name}}"></a></li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                                <div id="Quan" class="tabcontent1">
                                    <img class="desktop" src="{{asset('./assets/wiki/images/bg-quan.jpg')}}" alt="Nước Quần">
                                    <img class="mobile" src="{{asset('./assets/wiki/images/bg-quan-m.jpg')}}" alt="Nước Quần">
                                    <div class="general_card">
                                        <div class="title f-SVN-Helvetica-Neue-Bold">Tổ đội 3 tướng Quần Hùng - Liên Minh Quần Hùng - Tất cả ST phải chịu giảm 8%</div>
                                        <ul>
                                            @foreach($quan as $k => $item)
                                                <li><a href="{{route('general_detail', $item->domain_name)}}"><img src="{{$item->image}}" alt="{{$item->name}}"></a></li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                                <script>
                                    function openCity(evt, cityName) {
                                        var i, tabcontent, tablinks;
                                        tabcontent = document.getElementsByClassName("tabcontent1");
                                        for (i = 0; i < tabcontent.length; i++) {
                                            tabcontent[i].style.display = "none";
                                        }
                                        tablinks = document.getElementsByClassName("tablinks");
                                        for (i = 0; i < tablinks.length; i++) {
                                            tablinks[i].className = tablinks[i].className.replace(" active", "");
                                        }
                                        document.getElementById(cityName).style.display = "block";
                                        evt.currentTarget.className += " active";
                                    }
                                    // Get the element with id="defaultOpen" and click on it
                                    document.getElementById("defaultOpen").click();
                                </script>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>


        <section id="footer" class="section hvvh_mainsite_footer scrollFrame" data-block-id="hvvh_mainsite_footer">
            <div class="section__background">
                <img data-src="{{asset('./assets/wiki/images/footer-bg.png')}}" class="desktop lazyload" alt="">
                <img data-src="{{asset('./assets/wiki/images/footer-bg-m.png')}}" class="mobile lazyload" alt="">
                <span class="scrollwatch-pin"></span>
            </div>
            <div class="section__content">
                <div class="inner inner--hvvh_mainsite_footer f-SVN-Helvetica-Neue"><img src="{{asset('./assets/wiki/images/logovtc.png')}}" alt="">
                    <p>
                        Công ty cổ phần VTC Dịch vụ di động - Tầng 11, Tòa nhà VTC Online<br>
                        Số 18 Tam Trinh phường Minh Khai, quận Hai Bà Trưng, Hà Nội<br>
                        SĐT : (84-4).39877470 - Email : <EMAIL><br>
                        Người chịu trách nhiệm quản lý nội dung: Bà Trần Thị Thanh Hằng.<br>
                        Giấy phép phê duyệt nội dung kịch bản trò chơi điện tử trên mạng số 753/GP-BTTT<br>
                        Hướng dẫn cài đặt - Điều khoản
                    </p>
                </div>
            </div>
        </section>
    </div>
</div>

<div id="floating" class="floating">
    <div id="hvvh_mainsite_float_top_VfOgs" class="floattop hvvh_mainsite_float_top scaleDesktop scaleMobile" data-block-id="hvvh_mainsite_float_top">
        <div class="floattop__background"></div>
        <div class="floattop__content f-SVN-Helvetica-Neue-Bold">
            <a href="{{route('index')}}" class="iconapp desktop"><img src="{{asset('./assets/wiki/images/logo.png')}}" alt=""></a>
            <ul id="floatnav" class="floattop__nav floatnav">
                <li><a href="{{route('gioi_thieu')}}" class="floatnav__item " title="Giới thiệu">Giới thiệu</a></li>
                <li class="desktop"><img src="{{asset('./assets/wiki/images/ic-topmenu.png')}}" alt=""></li>
                <li><a href="{{route('index')}}" class="floatnav__item active" title="Danh tướng">Danh tướng</a></li>
                <li class="desktop"><img src="{{asset('./assets/wiki/images/ic-topmenu.png')}}" alt=""></li>
                <li><a href="{{route('tin_tuc')}}" class="floatnav__item " title="Tin tức">Tin tức</a></li>
                <li>
                    <div class="search-container">
                        <form action="">
                            <button type="submit"><i class="fa fa-search"></i></button>
                            <input type="text" id="searchInput" placeholder="Tìm kiếm" name="search">
                        </form>
                    </div>
                </li>
            </ul>
            <div class="group__icon mobile-flex">
                <a href="#" class="appicon"><img src="{{asset('./assets/wiki/images/icon.png')}}" alt=""></a>
                <a href="" data-track="Topup-Mainsite-Topbar" target="_blank" class="icon__topup "> <img src="{{asset('./assets/wiki/images/bt-download.png')}}" alt=""></a>
                <a href="#" target="_blank" data-track="DownloadOnelink-Mainsite-Topbar" class="icon__download "><img src="{{asset('./assets/wiki/images/bt-napthe.png')}}" alt=""></a>
            </div>
            <span id="navBurger" class="floattop__item floattop__item--burger mobile"><img src="{{asset('./assets/wiki/images/float_top/burger.png')}}" alt=""></span>
        </div>
    </div>

    <div id="common_required_loading_6RLL9" class="required_block required_block--common_required_loading common_required_loading" data-required-block-id="common_required_loading">
        <div class="loading active">
            <div class="multi-ripple">
                <div></div>
                <div></div>
            </div>
        </div>
    </div>

</div>

<script type="text/javascript" src="https://unpkg.com/jquery@3.7.1/dist/jquery.js"></script>
<script type="text/javascript" src="https://unpkg.com/lazysizes@5.3.2/lazysizes.min.js"></script>
<script type="text/javascript" src="https://unpkg.com/twig@1.17.1/twig.min.js"></script>

<script>
    window.onload = function () {
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab');
        if (tab != null) {
            if(tab == 'Nguy'){
                document.getElementById("btn-nguy").click();
            }
            if(tab == 'Thuc'){
                document.getElementById("btn-thuc").click();
            }
            if(tab == 'Ngo'){
                document.getElementById("btn-ngo").click();
            }
            if(tab == 'Quan'){
                document.getElementById("btn-quan").click();
            }
        }else {
            document.getElementById("btn-nguy").click();
        }
    }
</script>

<script>
    let timeout = null;

    $('#searchInput').on('input', function () {
        const query = $(this).val();
        clearTimeout(timeout);

        timeout = setTimeout(function () {
            if (query.trim() !== '') {
                // Thay 'tentrang' bằng route name tương ứng
                const baseUrl = '{{ route("wiki.search") }}'; // Laravel Blade sẽ render URL ở đây
                const newUrl = `${baseUrl}?search=${encodeURIComponent(query)}`;

                // Chuyển hướng trình duyệt
                window.location.href = newUrl;
            }
        }, 2000);
    });
</script>
<script>
    window.addEventListener('load', function () {
        // Tạo và chèn script 1
        const script1 = document.createElement('script');
        script1.src = "{{ asset('./assets/wiki/js/0.prod.bundle.js') }}";
        script1.type = "text/javascript";
        document.body.appendChild(script1);

        // Sau khi script1 load xong thì mới load script2
        script1.onload = function () {
            const script2 = document.createElement('script');
            script2.src = "{{ asset('./assets/wiki/js/prod.bundle.js') }}";
            script2.type = "text/javascript";
            document.body.appendChild(script2);
        };
    });
</script>

</body>
</html>

