<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="{{asset('./assets/wiki/images/favicon.ico')}}" type="image/x-icon" rel="shortcut icon" />
    <title>Tam <PERSON> Legend</title>
    <meta name="author" content="VTC" />
    <meta name="description" content="Game Chiến Thuật Hành Động: Ban-<PERSON> Đỉnh <PERSON>, Tự <PERSON> Tướng">
    <meta name="keywords" content="tam quoc,3Q,chien thuat,nhap vai,gacha,huyen thoai,3d,game hay vtc,vtc,vng,funtap,soha, hanh đong,RPG,SLG">
    <meta property="og:site_name" content="Tam Quốc Legend"/>
    <meta property="og:type" content="website"/>
    <meta property="og:url" content="https://tamquoclegend.splay.vn"/>
    <meta property="og:title" content="Tam <PERSON>"/>
    <meta property="og:description" content="Vô H<PERSON>, <PERSON><PERSON>c Lợi Khủng, Game Chiến Thuật Hành Động: Ban-Pick Đỉnh Cao, Tự Do Khiển Tướng"/>
    <meta property="og:image:type" content="image/jpeg"/>
    <meta property="og:image" content="https://tamquoclegend.splay.vn/sharefb.jpg')}}"/>

    <link rel="stylesheet" href="{{asset('./assets/wiki/css/main.css')}}"/>
    <link href="{{asset('./assets/wiki/css/0.prod.css')}}" rel="stylesheet">
    <link href="{{asset('./assets/wiki/css/prod.css')}}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css')}}">
</head>
<body class="">
<div id="wrapper" class="wrapper wrapper--homepage scaleDesktop scaleMobile">
    <div id="wrapperContent" class="wrapper__content">

        <section class="section hvvh_mainsite_main_news scrollFrame" data-block-id="hvvh_mainsite_main_news">
            <div class="section__background">
                <span class="scrollwatch-pin"></span>
            </div>
            <div class="section__content">
                <div class="inner inner--hvvh_mainsite_main_news">
                    <div class="container flex space-between">
                        <div class="main-content">
                            <div class="general-detail">
                                <div class="general-detail-menu f-SVN-Helvetica-Neue-Bold">
                                    <a href="{{route('index', ['tab' => 'Nguy'])}}" class="{{$detail->type == 1 ? 'active' : ''}}">NGỤY</a>
                                    <a href="{{route('index', ['tab' => 'Thuc'])}}" class="{{$detail->type == 2 ? 'active' : ''}}">THỤC</a>
                                    <a href="{{route('index', ['tab' => 'Ngo'])}}" class="{{$detail->type == 3 ? 'active' : ''}}">NGÔ</a>
                                    <a href="{{route('index', ['tab' => 'Quan'])}}" class="{{$detail->type == 4 ? 'active' : ''}}">QUẦN</a>
                                </div>
                                <div class="general-detail-introduce">
                                    <div class="imgbox"><img src="{{$detail->image}}" alt="{{$detail->name}}" class="img-responsive"></div>
                                    <div class="text f-SVN-Helvetica-Neue">
                                        <span><img src="{{asset('./assets/wiki/images/title-gt.png')}}" alt="Giới Thiệu" class="img-responsive"></span>
                                        <p>
                                            {{$detail->introduce}}
                                        </p>
                                    </div>
                                </div>
                                <div class="general-detail-skill">
                                    <span><img src="{{asset('./assets/wiki/images/title-knt.png')}}" alt="Kỹ năng tướng" class="img-responsive"></span>
                                    <p>
                                    <div class="table-rank tab-pane">
                                        <table class="table table-striped f-SVN-Helvetica-Neue">
                                            <thead>
                                            <tr class="f-SVN-Helvetica-Neue-Bold">
                                                <th>Tên kỹ năng</th>
                                                <th>Nội dung kỹ năng</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                                @for($i = 1; $i <= 7; $i++)
                                                    @if(!empty($detail["skill_name_$i"]))
                                                        <tr>
                                                            <td class="column1a">{{$detail["skill_name_$i"]}}</td>
                                                            <td class="column2a">{{$detail["skill_content_$i"]}}</td>
                                                        </tr>
                                                    @endif
                                                @endfor
                                            </tbody>
                                        </table>
                                    </div>
                                    </p>
                                </div>
                                <div class="general-detail-skill">
                                    <span><img src="{{asset('./assets/wiki/images/title-khtd.png')}}" alt="Kết hợp tổ đội" class="img-responsive"></span>
                                    <p>
                                    <div class="table-rank tab-pane">
                                        <table class="table table-striped table-hover f-SVN-Helvetica-Neue">
                                            <thead>
                                            <tr class="f-SVN-Helvetica-Neue-Bold">
                                                <th>Tướng</th>
                                                <th>Team</th>
                                                <th>Tên hiệu quả</th>
                                                <th>Nội dung</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            @for($i = 1; $i < 8; $i++)
                                                @if($i == 1)
                                                    <tr>
                                                        <td class="column1a" rowspan="{{countCombine($detail)}}">{{$detail->name}}</td>
                                                        <td class="column2a" style="white-space: pre-line;">{!! $detail["combine_team_$i"] !!}</td>
                                                        <td class="column1b">{{$detail["combine_name_$i"]}}</td>
                                                        <td class="column2c">{{$detail["combine_content_$i"]}}</td>
                                                    </tr>
                                                @else
                                                    @if(!empty($detail["combine_team_$i"]))
                                                        <tr>
                                                            <td class="column2a" style="white-space: pre-line;">{!! $detail["combine_team_$i"] !!}</td>
                                                            <td class="column1b">{{$detail["combine_name_$i"]}}</td>
                                                            <td class="column2c">{{$detail["combine_content_$i"]}}</td>
                                                        </tr>
                                                    @endif
                                                @endif
                                            @endfor
                                            </tbody>
                                        </table>
                                    </div>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="footer" class="section hvvh_mainsite_footer scrollFrame" data-block-id="hvvh_mainsite_footer">
            <div class="section__background">
                <img data-src="{{asset('./assets/wiki/images/footer-bg.png')}}" class="desktop lazyload" alt="">
                <img data-src="{{asset('./assets/wiki/images/footer-bg-m.png')}}" class="mobile lazyload" alt="">
                <span class="scrollwatch-pin"></span>
            </div>
            <div class="section__content">
                <div class="inner inner--hvvh_mainsite_footer f-SVN-Helvetica-Neue"><img src="{{asset('./assets/wiki/images/logovtc.png')}}" alt="">
                    <p>
                        Công ty cổ phần VTC Dịch vụ di động - Tầng 11, Tòa nhà VTC Online<br>
                        Số 18 Tam Trinh phường Minh Khai, quận Hai Bà Trưng, Hà Nội<br>
                        SĐT : (84-4).39877470 - Email : <EMAIL><br>
                        Người chịu trách nhiệm quản lý nội dung: Bà Trần Thị Thanh Hằng.<br>
                        Giấy phép phê duyệt nội dung kịch bản trò chơi điện tử trên mạng số 753/GP-BTTT<br>
                        Hướng dẫn cài đặt - Điều khoản
                    </p>
                </div>
            </div>
        </section>
    </div>
</div>

<div id="floating" class="floating">
    <div id="hvvh_mainsite_float_top_VfOgs" class="floattop hvvh_mainsite_float_top scaleDesktop scaleMobile" data-block-id="hvvh_mainsite_float_top">
        <div class="floattop__background"></div>
        <div class="floattop__content f-SVN-Helvetica-Neue-Bold">
            <a href="{{route('index')}}" class="iconapp desktop"><img src="{{asset('./assets/wiki/images/logo.png')}}" alt=""></a>
            <ul id="floatnav" class="floattop__nav floatnav">
                <li><a href="{{route('gioi_thieu')}}" class="floatnav__item " title="Giới thiệu">Giới thiệu</a></li>
                <li class="desktop"><img src="{{asset('./assets/wiki/images/ic-topmenu.png')}}" alt=""></li>
                <li><a href="{{route('index')}}" class="floatnav__item active" title="Danh tướng">Danh tướng</a></li>
                <li class="desktop"><img src="{{asset('./assets/wiki/images/ic-topmenu.png')}}" alt=""></li>
                <li><a href="{{route('tin_tuc')}}" class="floatnav__item " title="Tin tức">Tin tức</a></li>
                <li>
                    <div class="search-container">
                        <form action="">
                            <button type="submit"><i class="fa fa-search"></i></button>
                            <input type="text" id="searchInput" placeholder="Tìm kiếm" name="search">
                        </form>
                    </div>
                </li>
            </ul>
            <div class="group__icon mobile-flex">
                <a href="#" class="appicon"><img src="{{asset('./assets/wiki/images/icon.png')}}" alt=""></a>
                <a href="" data-track="Topup-Mainsite-Topbar" target="_blank" class="icon__topup "> <img src="{{asset('./assets/wiki/images/bt-download.png')}}" alt=""></a>
                <a href="#" target="_blank" data-track="DownloadOnelink-Mainsite-Topbar" class="icon__download "><img src="{{asset('./assets/wiki/images/bt-napthe.png')}}" alt=""></a>
            </div>
            <span id="navBurger" class="floattop__item floattop__item--burger mobile"><img src="{{asset('./assets/wiki/images/float_top/burger.png')}}" alt=""></span>
        </div>
    </div>

    <div id="common_required_loading_6RLL9" class="required_block required_block--common_required_loading common_required_loading" data-required-block-id="common_required_loading">
        <div class="loading active">
            <div class="multi-ripple">
                <div></div>
                <div></div>
            </div>
        </div>
    </div>

</div>

<script type="text/javascript" src="https://unpkg.com/jquery@3.7.1/dist/jquery.js"></script>
<script type="text/javascript" src="https://unpkg.com/lazysizes@5.3.2/lazysizes.min.js"></script>
<script type="text/javascript" src="https://unpkg.com/twig@1.17.1/twig.min.js"></script>

<script type="text/javascript" src="{{asset('./assets/wiki/js/0.prod.bundle.js')}}"></script>
<script type="text/javascript" src="{{asset('./assets/wiki/js/prod.bundle.js')}}"></script>
<script>
    let timeout = null;

    $('#searchInput').on('input', function () {
        const query = $(this).val();
        clearTimeout(timeout);

        timeout = setTimeout(function () {
            if (query.trim() !== '') {
                // Thay 'tentrang' bằng route name tương ứng
                const baseUrl = '{{ route("wiki.search") }}'; // Laravel Blade sẽ render URL ở đây
                const newUrl = `${baseUrl}?search=${encodeURIComponent(query)}`;

                // Chuyển hướng trình duyệt
                window.location.href = newUrl;
            }
        }, 2000);
    });
</script>
</body>
</html>

