@extends('layout.admin.master')
@section('css')
    <link rel="stylesheet" href="./assets/plugins/datatables-bs4/css/dataTables.bootstrap4.css'">
@endsection
@section('title')
    <PERSON>u<PERSON><PERSON> lý danh mục
@endsection
@section('bread_crumb')
    Quản lý danh mục
@endsection
@section('head_name')
    <PERSON><PERSON> sách
@endsection
@section('main-body')
    <div class="col-md-12">
        <a role="button"class="btn btn-primary float-right" href="{{route('admin.category.form.get')}}">
            Tạo mới
        </a>
    </div>
    <div class="card-body" style="width: 100%; overflow: scroll">
        <table id="product" class="table table-bordered table-striped" style="margin-top: 0">
            <thead>
            <tr>
                <th>STT</th>
                <th><PERSON>h mục</th>
                <th><PERSON>h mục cha</th>
                <th><PERSON><PERSON><PERSON> động</th>
            </tr>
            </thead>
            <tbody>
            @foreach($categories as $index => $item)
                <tr>
                    <td>{{$index + 1 }}</td>
                    <td>{{$item['name']}}</td>
                    <td>{{$item->parent_id ? config('app.danh_muc_huong_dan')[$item->parent_id] : ''}}</td>
                    <td class="text-center">
                        <a role="button"href="{{route('admin.category.form.edit', $item->id)}}"><i class="fa fa-edit"></i></a>
                        <a role="button"href="{{route('admin.category.delete', $item->id)}}"><i class="fa fa-trash-o"></i></a>
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
@endsection
@section('js')
    <script src="{{asset('/assets/plugins/datatables/jquery.dataTables.js')}}"></script>
    <script src="{{asset('/assets/plugins/datatables-bs4/js/dataTables.bootstrap4.js')}}"></script>

    <script>
        $(function () {
            $('#product').DataTable();
        });
    </script>
@endsection
