@extends('layout.admin.master')
@section('css')
    <link rel="stylesheet" href="./assets/plugins/datatables-bs4/css/dataTables.bootstrap4.css'">
@endsection
@section('title')
    Quản lý bài viết
@endsection
@section('bread_crumb')
    Quản lý bài viết
@endsection
@section('head_name')
    Danh sách bài viết
@endsection
@section('main-body')
    <div class="col-md-12">
        <a role="button"class="btn btn-primary float-right" href="{{route('admin.post.form.get')}}">
            Tạo mới item
        </a>
    </div>
    <div class="card-body" style="width: 100%; overflow: scroll">
        <table id="product" class="table table-bordered table-striped" style="margin-top: 0">
            <thead>
            <tr>
                <th>STT</th>
                <th>Tiêu đ<PERSON></th>
                <th><PERSON><PERSON><PERSON><PERSON> dẫn</th>
                <th>Ảnh</th>
                <th><PERSON><PERSON> mụ<PERSON></th>
                <th>Trạng thái</th>
                <th>Hành động</th>
            </tr>
            </thead>
            <tbody>
            @foreach($posts as $index => $item)
                <tr>
                    <td>{{$index + 1 }}</td>
                    <td>{{$item['title']}}</td>
                    <td><a href="{{getLink($item->title_domain, $item->category_id)}}" title="{{$item->title}}" target="_blank">{{getLink($item->title_domain, $item->category_id)}}</a></td>
                    <td>
                        <img style="width: 100px; height: 100px" src="{{$item->image}}">
                    </td>
                    <td>{{config('app.news_type')[$item->category_id]}}</td>
                    <td>{{config('base.active')[$item->active]}}</td>
                    <td class="text-center">
                        <a role="button" href="{{route('admin.post.form.edit', $item->id)}}"><i class="fa fa-edit"></i></a>
                        <a role="button" href="{{route('admin.post.delete', $item->id)}}"><i class="fa fa-trash-o"></i></a>
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
@endsection
@section('js')
    <script src="{{asset('/assets/plugins/datatables/jquery.dataTables.js')}}"></script>
    <script src="{{asset('/assets/plugins/datatables-bs4/js/dataTables.bootstrap4.js')}}"></script>

    <script>
        $(function () {
            $('#product').DataTable();
        });
    </script>
@endsection
