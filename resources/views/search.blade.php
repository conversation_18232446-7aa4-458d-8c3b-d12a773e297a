<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="{{asset('./assets/wiki/images/favicon.ico')}}" type="image/x-icon" rel="shortcut icon" />
    <title>Tam <PERSON> Legend</title>
    <meta name="author" content="VTC" />
    <meta name="description" content="Game Chiến Thuật Hành Động: Ban-<PERSON> Đỉnh <PERSON>, Tự <PERSON> Tướng">
    <meta name="keywords" content="tam quoc,3Q,chien thuat,nhap vai,gacha,huyen thoai,3d,game hay vtc,vtc,vng,funtap,soha, hanh đong,RPG,SLG">
    <meta property="og:site_name" content="Tam Quốc Legend"/>
    <meta property="og:type" content="website"/>
    <meta property="og:url" content="https://tamquoclegend.splay.vn"/>
    <meta property="og:title" content="Tam <PERSON>"/>
    <meta property="og:description" content="Vô H<PERSON>, <PERSON><PERSON>c Lợi Khủng, Game Chiến Thuật Hành Động: Ban-Pick Đỉnh Cao, Tự Do Khiển Tướng"/>
    <meta property="og:image:type" content="image/jpeg"/>
    <meta property="og:image" content="https://tamquoclegend.splay.vn/sharefb.jpg')}}"/>

    <link rel="stylesheet" href="{{asset('./assets/wiki/css/main.css')}}"/>
    <link href="{{asset('./assets/wiki/css/0.prod.css')}}" rel="stylesheet">
    <link href="{{asset('./assets/wiki/css/prod.css')}}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css')}}">
    <style>
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            display: none; /* Hiện khi cần */
            align-items: center;
            justify-content: center;
            background-color: rgba(0,0,0,0.6);
            padding: 20px;
            box-sizing: border-box;
        }

        .modal-content {
            position: relative;
            background-color: #fff;
            border-radius: 8px;
            width: 100%;
            max-width: 600px;
            aspect-ratio: 16 / 9;
            box-shadow: 0 5px 15px rgba(0,0,0,.5);
            overflow: hidden;
        }

        .modal-content iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }

        .close {
            position: absolute;
            top: 8px;
            right: 12px;
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            z-index: 10;
        }

        .close:hover {
            color: #000;
        }
    </style>
</head>
<body class="">
<div id="wrapper" class="wrapper wrapper--homepage scaleDesktop scaleMobile">
    <div id="wrapperContent" class="wrapper__content">

        <section class="section hvvh_mainsite_main_news scrollFrame" data-block-id="hvvh_mainsite_main_news">
            <div class="section__background">
                <span class="scrollwatch-pin"></span>
            </div>
            <div class="section__content">
                <div class="inner inner--hvvh_mainsite_main_news">
                    <div class="container flex space-between">
                        <div class="main-content">
                            <div class="general-news">
                                @foreach($news as $i => $post)
                                    <div class="news_list">
                                        <li><a href="{{route('tin_tuc_detail', ['post' => $post->title_domain])}}"><div class="thumb"><img src="{{'/wiki'.$post->image}}" class="img-responsive" alt=""></div></a>
                                        </li>
                                        <li class="f-SVN-Helvetica-Neue">
                                            <div class="title f-SVN-Helvetica-Neue-Bold"><a href="{{route('tin_tuc_detail', ['post' => $post->title_domain])}}">{{$post->title}}</a></div>
                                            <div class="time">{{\Carbon\Carbon::parse($post->created_at)->format('d-m-Y')}}</div>
                                            <p>{{$post->description}}</p>
                                            <div class="readmore"><a href="{{route('tin_tuc_detail', ['post' => $post->title_domain])}}">Xem thêm-></a></div>
                                        </li>
                                    </div>
                                @endforeach
{{--                                <div class="pagin-container f-SVN-Helvetica-Neue">--}}
{{--                                    @if($news->lastPage() > 1)--}}
{{--                                        <a class="prev" href="javascript:void(0)" onclick="paginationTinTuc({{$news->currentPage() - 1}})">--}}
{{--                                            <img src="./assets/images/prev.png" alt="">--}}
{{--                                        </a>--}}
{{--                                    @endif--}}
{{--                                    @for($i = $news->currentPage(); $news->lastPage() - $news->currentPage() > 9 ? $i <= $news->currentPage() + 9 : $i <= $news->lastPage(); $i++)--}}
{{--                                        <a href="javascript:void(0)" onclick="paginationTinTuc({{$i}})" class="page {{$i == $news->currentPage() ? 'active' : ''}}">{{$i}}</a>--}}
{{--                                    @endfor--}}
{{--                                    @if($news->currentPage() < $news->lastPage())--}}
{{--                                        <a class="next" href="javascript:void(0)" onclick="paginationTinTuc({{$news->currentPage() + 1}})">--}}
{{--                                            <img src="./assets/images/next.png" alt="">--}}
{{--                                        </a>--}}
{{--                                    @endif--}}
{{--                                </div>--}}

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="footer" class="section hvvh_mainsite_footer scrollFrame" data-block-id="hvvh_mainsite_footer">
            <div class="section__background">
                <img data-src="{{asset('./assets/wiki/images/footer-bg.png')}}" class="desktop lazyload" alt="">
                <img data-src="{{asset('./assets/wiki/images/footer-bg-m.png')}}" class="mobile lazyload" alt="">
                <span class="scrollwatch-pin"></span>
            </div>
            <div class="section__content">
                <div class="inner inner--hvvh_mainsite_footer f-SVN-Helvetica-Neue"><img src="{{asset('./assets/wiki/images/logovtc.png')}}" alt="">
                    <p>
                        Công ty cổ phần VTC Dịch vụ di động - Tầng 11, Tòa nhà VTC Online<br>
                        Số 18 Tam Trinh phường Minh Khai, quận Hai Bà Trưng, Hà Nội<br>
                        SĐT : (84-4).39877470 - Email : <EMAIL><br>
                        Người chịu trách nhiệm quản lý nội dung: Bà Trần Thị Thanh Hằng.<br>
                        Giấy phép phê duyệt nội dung kịch bản trò chơi điện tử trên mạng số 753/GP-BTTT<br>
                        Hướng dẫn cài đặt - Điều khoản
                    </p>
                </div>
            </div>
        </section>
    </div>
</div>

<!-- Modal -->
<div id="videoModal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <iframe id="youtubeIframe"
                src=""
                allowfullscreen></iframe>
    </div>
</div>

<div id="floating" class="floating">
    <div id="hvvh_mainsite_float_top_VfOgs" class="floattop hvvh_mainsite_float_top scaleDesktop scaleMobile" data-block-id="hvvh_mainsite_float_top">
        <div class="floattop__background"></div>
        <div class="floattop__content f-SVN-Helvetica-Neue-Bold">
            <a href="{{route('index')}}" class="iconapp desktop"><img src="{{asset('./assets/wiki/images/logo.png')}}" alt=""></a>
            <ul id="floatnav" class="floattop__nav floatnav">
                <li><a href="{{route('gioi_thieu')}}" class="floatnav__item " title="Giới thiệu">Giới thiệu</a></li>
                <li class="desktop"><img src="{{asset('./assets/wiki/images/ic-topmenu.png')}}" alt=""></li>
                <li><a href="{{route('index')}}" class="floatnav__item" title="Danh tướng">Danh tướng</a></li>
                <li class="desktop"><img src="{{asset('./assets/wiki/images/ic-topmenu.png')}}" alt=""></li>
                <li><a href="{{route('tin_tuc')}}" class="floatnav__item" title="Tin tức">Tin tức</a></li>
                <li>
                    <div class="search-container">
                        <form action="">
                            <button type="submit"><i class="fa fa-search"></i></button>
                            <input type="text" id="searchInput" placeholder="Tìm kiếm" name="search">
                        </form>
                    </div>
                </li>
            </ul>
            <div class="group__icon mobile-flex">
                <a href="#" class="appicon"><img src="{{asset('./assets/wiki/images/icon.png')}}" alt=""></a>
                <a href="" data-track="Topup-Mainsite-Topbar" target="_blank" class="icon__topup "> <img src="{{asset('./assets/wiki/images/bt-download.png')}}" alt=""></a>
                <a href="#" target="_blank" data-track="DownloadOnelink-Mainsite-Topbar" class="icon__download "><img src="{{asset('./assets/wiki/images/bt-napthe.png')}}" alt=""></a>
            </div>
            <span id="navBurger" class="floattop__item floattop__item--burger mobile"><img src="{{asset('./assets/wiki/images/float_top/burger.png')}}" alt=""></span>
        </div>
    </div>

    <div id="common_required_loading_6RLL9" class="required_block required_block--common_required_loading common_required_loading" data-required-block-id="common_required_loading">
        <div class="loading active">
            <div class="multi-ripple">
                <div></div>
                <div></div>
            </div>
        </div>
    </div>

</div>

<script type="text/javascript" src="https://unpkg.com/jquery@3.7.1/dist/jquery.js"></script>
<script type="text/javascript" src="https://unpkg.com/lazysizes@5.3.2/lazysizes.min.js"></script>
<script type="text/javascript" src="https://unpkg.com/twig@1.17.1/twig.min.js"></script>

<script type="text/javascript" src="{{asset('./assets/wiki/js/0.prod.bundle.js')}}"></script>
<script type="text/javascript" src="{{asset('./assets/wiki/js/prod.bundle.js')}}"></script>
<script>
    window.onload = function () {
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab');
        if (tab) {
            if(tab == 'tin-tuc'){
                const btnNew = document.getElementById("btn-new");
                if (btnNew) btnNew.click();
            }
            if(tab == 'clip'){
                const btnClip = document.getElementById("btn-clip");
                if (btnClip) btnClip.click();
            }
        }
    }
    function clickVideo(videoId) {
        const iframe = document.getElementById("youtubeIframe");
        iframe.src = `https://www.youtube.com/embed/${videoId}?autoplay=1`;
        const modal = document.getElementById("videoModal");
        modal.style.display = "flex";
    }

    document.querySelector(".close").addEventListener("click", function () {
        const modal = document.getElementById("videoModal");
        const iframe = document.getElementById("youtubeIframe");
        iframe.src = ""; // Reset để dừng video
        modal.style.display = "none";
    });
    var filter = {};
    function paginationTinTuc(id) {
        filter.page = id;
        console.log(filter);
        event.preventDefault();
        $.ajax({
            url: '/tin-tuc/tin-tuc-pagination',
            method: 'GET',
            data: filter,
            traditional: true,
            success: function(response) {
                // Chèn nội dung vào khu vực chỉ định
                $('#news').html(response);
            },
            error: function() {
                alert('Có lỗi xảy ra khi tải nội dung');
            }
        });
    }

    function paginationClip(id) {
        console.log(id)
        filter.page = id;
        console.log(filter);
        event.preventDefault();
        $.ajax({
            url: '/tin-tuc/clip-pagination',
            method: 'GET',
            data: filter,
            traditional: true,
            success: function(response) {
                // Chèn nội dung vào khu vực chỉ định
                $('#clip').html(response);
            },
            error: function() {
                alert('Có lỗi xảy ra khi tải nội dung');
            }
        });
    }
</script>
<script>
    let timeout = null;

    $('#searchInput').on('input', function () {
        const query = $(this).val();
        clearTimeout(timeout);

        timeout = setTimeout(function () {
            if (query.trim() !== '') {
                // Thay 'tentrang' bằng route name tương ứng
                const baseUrl = '{{ route("wiki.search") }}'; // Laravel Blade sẽ render URL ở đây
                const newUrl = `${baseUrl}?search=${encodeURIComponent(query)}`;

                // Chuyển hướng trình duyệt
                window.location.href = newUrl;
            }
        }, 2000);
    });
</script>
</body>
</html>

