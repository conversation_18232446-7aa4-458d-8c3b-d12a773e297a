@foreach($news as $i => $post)
    <div class="news_list">
        <li><a href="{{route('tin_tuc_detail', ['post' => $post->title_domain])}}"><div class="thumb"><img src="{{$post->image}}" class="img-responsive" alt=""></div></a>
        </li>
        <li class="f-SVN-Helvetica-Neue">
            <div class="title f-SVN-Helvetica-Neue-Bold"><a href="{{route('tin_tuc_detail', ['post' => $post->title_domain])}}">{{$post->title}}</a></div>
            <div class="time">{{\Carbon\Carbon::parse($post->created_at)->format('d-m-Y')}}</div>
            <p>{{$post->description}}</p>
            <div class="readmore"><a href="{{route('tin_tuc_detail', ['post' => $post->title_domain])}}">Xem thêm-></a></div>
        </li>
    </div>
@endforeach
<div class="pagin-container f-SVN-Helvetica-Neue">
    @if($news->lastPage() > 1)
        <a class="prev" href="javascript:void(0)" onclick="paginationTinTuc({{$news->currentPage() - 1}})">
            <img src="{{asset('./assets/wiki/images/prev.png')}}" alt="">
        </a>
    @endif
    @for($i = $news->currentPage(); $news->lastPage() - $news->currentPage() > 9 ? $i <= $news->currentPage() + 9 : $i <= $news->lastPage(); $i++)
        <a href="javascript:void(0)" onclick="paginationTinTuc({{$i}})" class="page {{$i == $news->currentPage() ? 'active' : ''}}">{{$i}}</a>
    @endfor
    @if($news->currentPage() < $news->lastPage())
        <a class="next" href="javascript:void(0)" onclick="paginationTinTuc({{$news->currentPage() + 1}})">
            <img src="{{asset('./assets/wiki/images/next.png')}}" alt="">
        </a>
    @endif
</div>
