@foreach($clips as $i => $post)
    <div class="news_list">
        <li><a onclick="clickVideo('{{$post->youtube_id}}')" href="javascript:void(0)"><div class="thumb"><img src="{{$post->image}}" class="img-responsive" alt=""></div></a>
        </li>
        <li class="f-SVN-Helvetica-Neue">
            <div class="title f-SVN-Helvetica-Neue-Bold"><a onclick="clickVideo('{{$post->youtube_id}}')" href="javascript:void(0)">{{$post->title}}</a></div>
            <div class="time">{{\Carbon\Carbon::parse($post->created_at)->format('d-m-Y')}}</div>
            <p>{{$post->description}}</p>
        </li>
    </div>
@endforeach
<div class="pagin-container f-SVN-Helvetica-Neue">
    @if($clips->lastPage() > 1)
        <a class="prev" href="javascript:void(0)" onclick="paginationClip({{$clips->currentPage() - 1}})">
            <img src="./assets/images/prev.png" alt="">
        </a>
    @endif
    @for($i = $clips->currentPage(); $clips->lastPage() - $clips->currentPage() > 9 ? $i <= $clips->currentPage() + 9 : $i <= $clips->lastPage(); $i++)
        <a href="javascript:void(0)" onclick="paginationClip({{$i}})" class="page {{$i == $clips->currentPage() ? 'active' : ''}}">{{$i}}</a>
    @endfor
    @if($clips->currentPage() < $clips->lastPage())
        <a class="next" href="javascript:void(0)" onclick="paginationClip({{$clips->currentPage() + 1}})">
            <img src="./assets/images/next.png" alt="">
        </a>
    @endif
</div>
