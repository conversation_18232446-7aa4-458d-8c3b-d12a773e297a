<!DOCTYPE html>
<html lang="vi" xmlns="http://www.w3.org/1999/xhtml">

<head>
    @yield('custom-header')
    <!-- Meta Pixel Code -->
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '2076632272854626');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
                   src="https://www.facebook.com/tr?id=2076632272854626&ev=PageView&noscript=1"
        /></noscript>
    <!-- End Meta Pixel Code -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link href="{{asset('assets/homepage/css/bootstrap.min.css')}}" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="{{asset('assets/homepage/css/style.css')}}">
    <link rel="stylesheet" href="{{asset('assets/homepage/css/swiper-bundle.min.css')}}">
    <link rel="stylesheet" href="{{asset('assets/homepage/css/mcgic.css')}}" />
    <link rel="stylesheet" href="{{asset('assets/homepage/css/jquery.fancybox.min.css')}}" />
    <script src="{{asset('assets/homepage/js/bootstrap.bundle.min.js')}}"></script>
    <script src="{{asset('assets/homepage/js/jquery-1.9.1.min.js')}}"></script>
    <script src="{{asset('assets/homepage/js/swiper-bundle.min.js')}}"></script>
    <link rel="apple-touch-icon" href="{{asset('./assets/homepage/images/logo-favico.webp')}}">
    <link rel="icon" href="{{asset('./assets/homepage/images/logo-favico.webp')}}" type="image/png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/limonte-sweetalert2/6.6.9/sweetalert2.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/limonte-sweetalert2/6.6.9/sweetalert2.min.js"></script>

    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-KV6DK33X');</script>
    <!-- End Google Tag Manager -->

</head>

<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KV6DK33X"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

<div class="wrapper">
    <div class="warning">
        <img class="img-fluid" src="{{asset('./assets/homepage/images/warning-18.jpg')}}" alt="" />
    </div>

    <div class="header">
        <div class="container1  d-flex justify-content-between">
            <h1 class="logo"><a href="{{route('index')}}" title=""><img class="img-fluid" src="{{asset('./assets/homepage/images/logo-cabal.png')}}"
                                                                alt="" /></a></h1>
            <ul class="navbar-nav d-flex justify-content-between flex-row">
                <li class="nav-item">
                    <a class="nav-link" href="{{route('gioi_thieu')}}">Giới thiệu</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{route('tin_tuc')}}">Tin tức</a>
                </li>
                <li class="nav-item dropdown soon">
                    <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#">Hướng dẫn</a>
                    <ul class="dropdown-menu bg-transparent border-0">
                        <li><a class="dropdown-item" href="#"><img class="img-fluid" src="{{asset('./assets/homepage/images/tag-sapramat.png')}}"
                                                                   alt="" /></a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown soon">
                    <a class="nav-link text-center dropdown-toggle" data-bs-toggle="dropdown" href="#">Diễn đàn</a>
                    <ul class="dropdown-menu bg-transparent border-0">
                        <li><a class="dropdown-item" href="#"><img class="img-fluid" src="{{asset('./assets/homepage/images/tag-sapramat.png')}}"
                                                                   alt="" /></a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#">Cộng đồng</a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="https://www.facebook.com/cabal.vtczone.vn">Fanpage</a></li>
                        <li><a class="dropdown-item" href="https://www.facebook.com/groups/cabalvtc.vn?locale=vi_VN">Group</a></li>
                        <li><a class="dropdown-item" href="https://youtu.be/-O_yUC055f8?si=QPaOg5q68NraqMkP">YouTube</a></li>
                        <li><a class="dropdown-item" href="https://vt.tiktok.com/ZSMSsuV66/">Tiktok</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    @if(!session()->has('member'))
                        <a class="nav-link btn-login" href="https://graph.vtcmobile.vn/oauth/authorize?client_id=5841b5a8bcb690e2664b9ef070d4c235&redirect_uri=<?php echo e(route('member.login', ['redirect' => url()->full()])); ?>">
                            <img class="img-fluid" src="./assets/moiban/images/btn-dangnhap.png" alt="" />
                        </a>
                    @else
                        <p style="margin-top: 25px;color: white;">Xin chào: {{ session('member')['name'] }} <a class="text-info" href="{{config('base.urlBase') . "?client_id=" . config('base.client_id') . "&redirect_uri=" . route('member.logout', ['redirect' => url()->full()]) . "&agencyid=1&action=logout"}}" title="Thoát"> (Thoát)</a></p>
                    @endif
                </li>
            </ul>
        </div>
    </div>
    @yield('main-body')
    <section id="section6" class="section section6 mt-5 border-top border-dark">
        <div class="section_content">
            <div class="footer container1 d-flex justify-content-between align-items-center">
                <div class="footer-content">
                    <div class="vtc-logo d-flex justify-content-start align-items-center"> <img
                            src="./assets/homepage/images/logo-vtcmobile-white.png" class="img-fluid px-3" alt="">
                        <img src="./assets/homepage/images/logo-estgame.png" class="img-fluid px-3" alt="">
                    </div>
                    <div class="footer-text font-size-16">
                        <p class="mb-1">Copright 2025 VTC Mobile. All rights reserved.</p>
                        <p class="mb-1">Công ty cổ phần VTC Dịch vụ di động - Tầng 11, Tòa nhà VTC Online</p>
                        <p class="mb-1">Số 18 Tam Trinh phường Minh Khai, quận Hai Bà Trưng, Hà Nội</p>
                        <p class="mb-1">SĐT : (84-4).39877470 | Email : <EMAIL></p>
                        <p class="mb-1">Người chịu trách nhiệm quản lý nội dung: Ông Nguyễn Viết Quang Minh.</p>
                        <p class="mb-1">Giấy phép phê duyệt nội dung kịch bản trò chơi điện tử trên mạng số 30 QĐ-PTTH&TTĐT</p>
                        <p class="mb-1"><a href="https://cabal.vn/huong-dan/huong-dan-cai-dat-6.html" target="_blank">Chính sách cài đặt & gỡ bỏ</a> | <a href="https://cabal.vn/huong-dan/dieu-khoan-su-dung-12.html"
                                                                                                        target="_blank">Điều khoản sử dụng</a> | <a href="https://cabal.vn/huong-dan/chinh-sach-bao-mat-thong-tin-11.html" target="_blank">Chính sách bảo
                                mật</a></p>
                    </div>
                </div>
                <div class="warning-18 ps-3">
                    <div class="tag-18">
                        <img src="./assets/homepage/images/tag-18.png" class="img-fluid" alt="">

                    </div>
                    <p class="mb-1 pt-2 text-white">Đây là trò chơi miễn phí, một số nội dung trong trò chơi có chứa
                        cảnh bạo lực. <br />
                        Trò chơi cũng cung cấp các dịch vụ trả phí như mua vật phẩm trong trò chơi ảo. <br />
                        Hãy chú ý đến thời gian chơi và không nên quá 180 phút mỗi ngày.</p>
                </div>
            </div>

        </div>
    </section>
</div>

<!-- Menu float right -->
<div class="download active">
    <a class="download-btn download-btn-logo zoom-50" href="#" role="button" title="Cabal">
        <img src="{{asset('./assets/homepage/images/icon-game.png')}}" class="img-fluid brightness" alt="" />
    </a>
    <a class="btn-img download-btn-ios zoom-50" href="{{overrideTrackingDownloadUrl('https://dl.splay.vn/download-game')}}" role="button" title="Tải App Store">
        <span class="visually-hidden">Tải Game</span>
        <img src="{{asset('./assets/homepage/images/btn-taigame-right.png')}}" class="img-fluid brightness" alt="Tải App Store" />
    </a>
    <a class="btn-img download-btn zoom-50" target="_blank" href="https://scoin.vn/nap-cknh/131" role="button" title="Tải Google Play">
        <span class="visually-hidden">Nạp Game</span>
        <img src="{{asset('./assets/homepage/images/btn-napgame-right.png')}}" class="img-fluid brightness" alt="Tải Google Play" />
    </a>
    <a class="btn-img download-btn zoom-50" href="{{route('su_kien')}}" role="button" title="Sự kiện">
        <span class="visually-hidden">Sự kiện</span>
        <img src="{{asset('./assets/homepage/images/btn-hotro-right.png')}}" class="img-fluid brightness" alt="Tải APK" />
    </a>
    <!-- <div class="d-flex justify-content-between btn-group-social">

        <a class="px-1 zoom-50" href="#" title="Top">
            <span class="visually-hidden">Top</span>
            <img src="images/top.png" class="img-fluid brightness" alt="Top" />
        </a>
    </div> -->
    <a class="download-toggle" id="download-toggle" href="#">
        <span class="visually-hidden">Thu gọn</span>
    </a>
</div>
<!-- End Menu float right -->
<!-- The Modal Dang nhap-->
<div class="modal fade" id="popupInfo">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-info">
            <div class="modal-content bg-transparent border-0">

                <!-- Modal Header -->
                <div class="modal-header border-0">
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <h2 class="text-center"><img src="./assets/homepage/images/title-thongtindangky.png" class="img-fluid" alt="" />
                    </h2>
                    <div class="form-thongtin w-50 mx-auto">
                        <div class="form-floating my-3">
                            <input type="text" class="form-control" id="fullname" placeholder="Họ tên" name="">
                            <label for="">Họ tên</label>
                        </div>
                        <div class="form-floating my-3">
                            <input type="text" class="form-control" id="phone" placeholder="Số điện thoại" name="">
                            <label for="">Số điện thoại</label>
                        </div>
                        <div class="form-floating my-3">
                            <input type="text" class="form-control" id="email" placeholder="Email" name="">
                            <label for="">Email</label>
                        </div>
                        <div class="form-floating my-3">
                            <input type="text" class="form-control" id="scoin_user" placeholder="Tên tài khoản Scoin" name="">
                            <label for="">Tên tài khoản Scoin</label>
                        </div>
                        <p class="note-register">
                            Chưa có tài khoản Scoin? <a class="text-info text-decoration-none" href="https://vtcmobile.vn/oauth/accounts/mobile/AccountRegister.aspx?sid=330035&ur=https%3a%2f%2fgraph.vtcmobile.vn%2foauth%2fauthorize%3fclient_id%3db41f9e5a37b3ec9a021382b36946dbec%26redirect_uri%3dhttps%253a%252f%252fscoin.vn%252f%26urllogin%3d%26m%3d%26agencyid%3d1%26imei%3d&m=1&continue=https%3a%2f%2fgraph.vtcmobile.vn%2foauth%2fauthorize%3fclient_id%3db41f9e5a37b3ec9a021382b36946dbec%26redirect_uri%3dhttps%253a%252f%252fscoin.vn%252f%26urllogin%3d%26m%3d%26agencyid%3d1%26imei%3d&redirect_uri=https%3a%2f%2fgraph.vtcmobile.vn%2foauth%2fauthorize%3fclient_id%3db41f9e5a37b3ec9a021382b36946dbec%26redirect_uri%3dhttps%253a%252f%252fscoin.vn%252f%26urllogin%3d%26m%3d%26agencyid%3d1%26imei%3d&agencyid=1" target="_blank"
                                                        title="Đăng ký">Đăng ký ngay</a>
                        </p>
                        <div class="btn-submit zoom-50 pb-3">
                            <a href="javascript:void(0)" id="registerGiftCodeButton" title="Đăng nhập">
                                Đăng ký
                            </a>
                        </div>

                    </div>


                </div>



            </div>
        </div>
    </div>
</div>
<!-- The Modal Thong bao thanh cong-->
<div class="modal fade" id="popupSuccess">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-info">
            <div class="modal-content bg-transparent border-0">
                <!-- Modal Header -->
                <div class="modal-header border-0">
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <!-- Modal body -->
                <div class="modal-body">

                    <h2 class="text-center">
                        <img src="./assets/homepage/images/ic-check-success.png" class="img-fluid pt-4 pb-3" alt="" />
                        <br />
                        Đăng ký thành công
                    </h2>
                    <p class="note-register">
                        Đăng ký của bạn đã đăng ký thành công và được ghi nhận trong hệ thống <br />
                    </p>
                    <p class="note-register">
                        Không phải bạn? <a class="text-info text-decoration-none" href="javascript:void(0)" id="click-register1" title="đăng ký">Đăng ký bằng tài khoản khác</a>
                    </p>
                    <div class="btn-submit zoom-50">
                        <a href="{{route('index')}}" title="Trang chủ">
                            Trang chủ
                        </a>
                    </div>



                </div>



            </div>
        </div>
    </div>
</div>
<!-- The Modal Thong bao thatbai-->
<div class="modal fade" id="popupError">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-info">
            <div class="modal-content bg-transparent border-0">
                <!-- Modal Header -->
                <div class="modal-header border-0">
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <!-- Modal body -->
                <div class="modal-body">

                    <h2 class="text-center">
                        <img src="./assets/homepage/images/ic-question.png" class="img-fluid pt-4 pb-3" alt="" />
                        <br />
                        TÀI KHOẢN ĐÃ ĐĂNG KÝ
                    </h2>
                    <p class="note-register">
                        Tài khoản của bạn đã đăng ký thành công và được ghi nhận trong hệ thống. <br />
                    </p>
                    <p class="note-register">
                        Không phải bạn? <a class="text-info text-decoration-none" href="javascript:void(0)" id="click-register2" title="đăng ký">Đăng ký bằng tài khoản khác</a>
                    </p>
                    <div class="btn-submit zoom-50">
                        <a href="{{route('index')}}" title="Trang chủ">
                            Trang chủ
                        </a>
                    </div>



                </div>



            </div>
        </div>
    </div>
</div>
<!-- The Modal Thong bao du luot dang ký-->
<div class="modal fade" id="popupWarning">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-info">
            <div class="modal-content bg-transparent border-0">
                <!-- Modal Header -->
                <div class="modal-header border-0">
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <!-- Modal body -->
                <div class="modal-body">

                    <h2 class="text-center">
                        <img src="./assets/homepage/images/ic-question.png" class="img-fluid pt-4 pb-3" alt="" />
                        <br />
                        Hết lượt đăng ký
                    </h2>
                    <p class="note-register">
                        Hệ thống đã đủ lượt đăng ký, vui lòng theo dõi <a class="text-info text-decoration-none" target="_blank"
                                                                          href="https://www.facebook.com/cabal.vtczone.vn" title="Đăng ký">Fanpage</a>
                        <br />
                        của chúng tôi để nhận thông tin về các đợt đăng ký tiếp theo.
                    </p>
                    <div class="btn-submit zoom-50">
                        <a href="{{route('index')}}" title="Trang chủ">
                            Trang chủ
                        </a>
                    </div>



                </div>



            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="popupExist">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-info">
            <div class="modal-content bg-transparent border-0">
                <!-- Modal Header -->
                <div class="modal-header border-0">
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <!-- Modal body -->
                <div class="modal-body">

                    <h2 class="text-center">
                        <img src="./assets/homepage/images/ic-question.png" class="img-fluid pt-4 pb-3" alt="" />
                        <br />
                        Trùng dữ liệu
                    </h2>
                    <p class="note-register" id="message_note">
                    </p>
                    <div class="btn-submit zoom-50">
                        <a href="{{route('index')}}" title="Trang chủ">
                            Trang chủ
                        </a>
                    </div>



                </div>



            </div>
        </div>
    </div>
</div>
@yield('script')
<script>
    let popup;

    function openFacebookSignup() {
        $('#popupChonserver').hide();
        const popupWidth = 500; // Chiều rộng của popup
        const popupHeight = 600; // Chiều cao của popup
        const left = (window.screen.width / 2) - (popupWidth / 2);
        const top = (window.screen.height / 2) - (popupHeight / 2);

        // Mở popup tới URL đăng ký Facebook
        popup = window.open(
            'https://graph.vtcmobile.vn/oauth/authorize?client_id=5841b5a8bcb690e2664b9ef070d4c235&redirect_uri=http://localhost:8000/login/', // URL trang đăng ký Facebook
            'ScoinLogin',
            `width=${popupWidth},height=${popupHeight},top=${top},left=${left}`
        );
        const popupChecker = setInterval(() => {
            if (popup.closed) {
                clearInterval(popupChecker);
                window.location.reload(true);
                // Tùy chỉnh thêm hành động ở đây nếu cần
            }
        }, 500);
    }
    const copyButton = document.getElementById('copyButton');
    if (copyButton) {
        copyButton.addEventListener('click', function () {
            const textToCopy = document.getElementById('textToCopy');
            if (textToCopy) {
                navigator.clipboard.writeText(textToCopy.value)
                    .then(() => {
                        alert('Copy thành công');
                    })
                    .catch(err => {
                        alert('Copy thất bại');
                    });
            }
        });
    }
</script>
<script src="{{asset('assets/homepage/js/parallax.min.js')}}"></script>
<script src="{{asset('assets/homepage/js/res-1.js')}}"></script>
<script src="{{asset('assets/homepage/js/jquery.fancybox.min.js')}}"></script>
<script src="{{asset('assets/homepage/js/gsap.min.js')}}"></script>
<script src="{{asset('assets/homepage/js/ScrollTrigger.min.js')}}"></script>
<script src="{{asset('assets/plugin/js/ScrollTrigger.min.js')}}"></script>
</body>

</html>
