/*! For license information please see 0.prod.bundle.js.LICENSE.txt */
(window.webpackJsonp=window.webpackJsonp||[]).push([[0],{2:function(t,e,n){"use strict";var i=function(){return i=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};function o(t,e,n,i){return new(n||(n=Promise))((function(o,s){function a(t){try{l(i.next(t))}catch(t){s(t)}}function r(t){try{l(i.throw(t))}catch(t){s(t)}}function l(t){t.done?o(t.value):new n((function(e){e(t.value)})).then(a,r)}l((i=i.apply(t,e||[])).next())}))}function s(t,e){var n,i,o,s,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return s={next:r(0),throw:r(1),return:r(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function r(s){return function(r){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,i&&(o=2&s[0]?i.return:s[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,s[1])).done)return o;switch(i=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],i=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,r])}}}function a(t){return t.replace(/\s+/g,"-").toLowerCase()}function r(t){return new Promise((function(e,n){var i=new XMLHttpRequest;i.overrideMimeType("application/json"),i.open("GET",t,!0),i.onreadystatechange=function(){4===i.readyState&&(200!==i.status?n(new Error("Response has status code "+i.status)):e(i.responseText))},i.send()}))}!function(t,e){void 0===e&&(e={});var n=e.insertAt;if(t&&"undefined"!=typeof document){var i=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css","top"===n&&i.firstChild?i.insertBefore(o,i.firstChild):i.appendChild(o),o.styleSheet?o.styleSheet.cssText=t:o.appendChild(document.createTextNode(t))}}('@charset "UTF-8";\ndiv[id^=font-picker] {\n  position: relative;\n  display: inline-block;\n  width: 200px;\n  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);\n}\ndiv[id^=font-picker] * {\n  box-sizing: border-box;\n}\ndiv[id^=font-picker] p {\n  margin: 0;\n  padding: 0;\n}\ndiv[id^=font-picker] button {\n  color: inherit;\n  font-size: inherit;\n  background: none;\n  border: 0;\n  outline: none;\n  cursor: pointer;\n}\ndiv[id^=font-picker] .dropdown-button {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  height: 35px;\n  padding: 0 10px;\n  background: #cbcbcb;\n}\ndiv[id^=font-picker] .dropdown-button:hover, div[id^=font-picker] .dropdown-button:focus {\n  background: #bebebe;\n}\ndiv[id^=font-picker] .dropdown-button .dropdown-font-name {\n  overflow: hidden;\n  white-space: nowrap;\n}\ndiv[id^=font-picker] .dropdown-icon {\n  margin-left: 10px;\n}\n@-webkit-keyframes spinner {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes spinner {\n  to {\n    transform: rotate(360deg);\n  }\n}\ndiv[id^=font-picker] .dropdown-icon.loading::before {\n  display: block;\n  width: 10px;\n  height: 10px;\n  border: 2px solid #b2b2b2;\n  border-top-color: #000000;\n  border-radius: 50%;\n  -webkit-animation: spinner 0.6s linear infinite;\n          animation: spinner 0.6s linear infinite;\n  content: "";\n}\ndiv[id^=font-picker] .dropdown-icon.finished::before {\n  display: block;\n  width: 0;\n  height: 0;\n  margin: 0 2px;\n  border-top: 6px solid #000000;\n  border-right: 5px solid transparent;\n  border-left: 5px solid transparent;\n  transition: transform 0.3s;\n  content: "";\n}\ndiv[id^=font-picker] .dropdown-icon.error::before {\n  content: "⚠";\n}\ndiv[id^=font-picker].expanded .dropdown-icon.finished::before {\n  transform: rotate(-180deg);\n}\ndiv[id^=font-picker].expanded ul {\n  max-height: 200px;\n}\ndiv[id^=font-picker] ul {\n  position: absolute;\n  z-index: 1;\n  width: 100%;\n  max-height: 0;\n  margin: 0;\n  padding: 0;\n  overflow-x: hidden;\n  overflow-y: auto;\n  background: #eaeaea;\n  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);\n  transition: 0.3s;\n  -webkit-overflow-scrolling: touch;\n}\ndiv[id^=font-picker] ul li {\n  height: 35px;\n  list-style: none;\n}\ndiv[id^=font-picker] ul li button {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  padding: 0 10px;\n  white-space: nowrap;\n}\ndiv[id^=font-picker] ul li button:hover, div[id^=font-picker] ul li button:focus {\n  background: #dddddd;\n}\ndiv[id^=font-picker] ul li button.active-font {\n  background: #d1d1d1;\n}');function l(t){return o(this,void 0,void 0,(function(){var e,n,o;return s(this,(function(s){switch(s.label){case 0:return(e=new URL("https://www.googleapis.com/webfonts/v1/webfonts")).searchParams.append("sort","popularity"),e.searchParams.append("key",t),[4,r(e.href)];case 1:return n=s.sent(),o=JSON.parse(n),[2,o.items.map((function(t){var e=t.family,n=t.subsets,o=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(i=Object.getOwnPropertySymbols(t);o<i.length;o++)e.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(t,i[o])&&(n[i[o]]=t[i[o]])}return n}(t,["family","subsets"]);return i(i({},o),{family:e,id:a(e),scripts:n})}))]}}))}))}var c=document.createElement("style");function d(t,e,n){var i="\n\t\t.apply-font"+n+' {\n\t\t\tfont-family: "'+t.family+'"'+(e?', "'+e+'"':"")+";\n\t\t}\n\t",o=function(t){var e="active-font-"+t,n=document.getElementById(e);return n||((n=document.createElement("style")).id=e,document.head.appendChild(n)),n}(n);o.innerHTML=i}document.head.appendChild(c);var p="data-is-preview";function h(t){return"font-"+t}function f(t,e){var n=document.getElementById(h(t));return null==e?null!==n:null!==n&&n.getAttribute(p)===e.toString()}function u(t,e){var n=document.createElement("style");n.id=h(t),n.setAttribute(p,e.toString()),document.head.appendChild(n)}function g(t,e){var n=h(t),i=document.getElementById(n);i?i.textContent=e:console.error('Could not fill stylesheet: Stylesheet with ID "'+n+'" not found')}function v(t,e){var n,i=[];do{(n=t.exec(e))&&i.push(n[1])}while(n);return i}var m=/@font-face {([\s\S]*?)}/gm,b=/font-family: ['"](.*?)['"]/gm;var y="https://fonts.googleapis.com/css";function w(t,e,n,i){return o(this,void 0,void 0,(function(){var o,a,l,c,d;return s(this,(function(s){return o=new URL(y),a=n.join(","),l=t.map((function(t){return t.family+":"+a})),o.searchParams.append("family",l.join("|")),o.searchParams.append("subset",e.join(",")),i&&(c=t.map((function(t){return t.family})).join(""),d=c.split("").filter((function(t,e,n){return n.indexOf(t)===e})).join(""),o.searchParams.append("text",d)),o.searchParams.append("font-display","swap"),[2,r(o.href)]}))}))}function P(t,e,n,i){return o(this,void 0,void 0,(function(){var o,r,l,d;return s(this,(function(s){switch(s.label){case 0:return o=Array.from(t.values()),(r=o.map((function(t){return t.id})).filter((function(t){return!f(t)}))).forEach((function(t){return u(t,!0)})),[4,w(o,e,n,!0)];case 1:return l=s.sent(),d=function(t){var e=v(m,t),n={};return e.forEach((function(t){var e=a(v(b,t)[0]);e in n||(n[e]=""),n[e]+="@font-face {\n"+t+"\n}\n\n"})),n}(l),o.forEach((function(t){if(function(t,e){var n="\n\t\t\t#font-button-"+a(t.family)+e+' {\n\t\t\t\tfont-family: "'+t.family+'";\n\t\t\t}\n\t\t';c.appendChild(document.createTextNode(n))}(t,i),r.includes(t.id)){if(!(t.id in d))return void console.error('Missing styles for font "'+t.family+'" (fontId "'+t.id+'") in Google Fonts response');g(t.id,d[t.id])}})),[2]}}))}))}function x(t,e,n,i,a){return o(this,void 0,void 0,(function(){var o;return s(this,(function(s){switch(s.label){case 0:return f(t.id,!1)?(d(t,e,a),[3,3]):[3,1];case 1:return f(t.id,!0)?(r=t.id,l=!1,c=h(r),(v=document.getElementById(c))?v.setAttribute(p,l.toString()):console.error('Could not change stylesheet type: Stylesheet with ID "'+c+'" not found')):u(t.id,!1),[4,w([t],n,i,!1)];case 2:o=s.sent(),d(t,e,a),g(t.id,o),s.label=3;case 3:return[2]}var r,l,c,v}))}))}var k="Open Sans",C={pickerId:"",families:[],categories:[],scripts:["latin"],variants:["regular"],filter:function(){return!0},limit:50,sort:"alphabet"},E=function(){function t(t,e,n,i){void 0===e&&(e=k);var o=n.pickerId,s=void 0===o?C.pickerId:o,a=n.families,r=void 0===a?C.families:a,l=n.categories,c=void 0===l?C.categories:l,d=n.scripts,p=void 0===d?C.scripts:d,h=n.variants,f=void 0===h?C.variants:h,u=n.filter,g=void 0===u?C.filter:u,v=n.limit,m=void 0===v?C.limit:v,b=n.sort,y=void 0===b?C.sort:b;void 0===i&&(i=function(){}),this.fonts=new Map,function(t){if(t.match(/[^0-9a-z]/i))throw Error("The `pickerId` parameter may only contain letters and digits")}(s),this.selectorSuffix=s?"-"+s:"",this.apiKey=t,this.options={pickerId:s,families:r,categories:c,scripts:p,variants:f,filter:g,limit:m,sort:y},this.onChange=i,this.addFont(e,!1),this.setActiveFont(e,!1)}return t.prototype.init=function(){return o(this,void 0,void 0,(function(){var t,e,n,i,o;return s(this,(function(s){switch(s.label){case 0:return[4,l(this.apiKey)];case 1:for(t=s.sent(),e=function(e){var i=t[e];if(n.fonts.size>=n.options.limit)return"break";n.fonts.has(i.family)||0!==n.options.families.length&&!n.options.families.includes(i.family)||0!==n.options.categories.length&&!n.options.categories.includes(i.category)||!n.options.scripts.every((function(t){return i.scripts.includes(t)}))||!n.options.variants.every((function(t){return i.variants.includes(t)}))||!0!==n.options.filter(i)||n.fonts.set(i.family,i)},n=this,i=0;i<t.length&&"break"!==e(i);i+=1);return(o=new Map(this.fonts)).delete(this.activeFontFamily),P(o,this.options.scripts,this.options.variants,this.selectorSuffix),[2,this.fonts]}}))}))},t.prototype.getFonts=function(){return this.fonts},t.prototype.addFont=function(t,e){void 0===e&&(e=!0);var n={family:t,id:a(t)};if(this.fonts.set(t,n),e){var i=new Map;i.set(t,n),P(i,this.options.scripts,this.options.variants,this.selectorSuffix)}},t.prototype.removeFont=function(t){this.fonts.delete(t)},t.prototype.getActiveFont=function(){var t=this.fonts.get(this.activeFontFamily);if(t)return t;throw Error('Cannot get active font: "'+this.activeFontFamily+'" is not in the font list')},t.prototype.setActiveFont=function(t,e){var n=this;void 0===e&&(e=!0);var i=this.activeFontFamily,o=this.fonts.get(t);if(!o)throw Error('Cannot update active font: "'+t+'" is not in the font list');this.activeFontFamily=t,x(o,i,this.options.scripts,this.options.variants,this.selectorSuffix).then((function(){e&&n.onChange(o)}))},t.prototype.setOnChange=function(t){this.onChange=t},t}(),F=function(){function t(t,e,n,i){void 0===e&&(e=k);var o=n.pickerId,s=void 0===o?C.pickerId:o,a=n.families,r=void 0===a?C.families:a,l=n.categories,c=void 0===l?C.categories:l,d=n.scripts,p=void 0===d?C.scripts:d,h=n.variants,f=void 0===h?C.variants:h,u=n.filter,g=void 0===u?C.filter:u,v=n.limit,m=void 0===v?C.limit:v,b=n.sort,y=void 0===b?C.sort:b;void 0===i&&(i=function(){}),this.expanded=!1,this.closeEventListener=this.closeEventListener.bind(this),this.toggleExpanded=this.toggleExpanded.bind(this);var w={pickerId:s,families:r,categories:c,scripts:p,variants:f,filter:g,limit:m,sort:y};this.fontManager=new E(t,e,w,i),this.generateUI(y)}return t.prototype.generateUI=function(t){var e=this,n="font-picker"+this.fontManager.selectorSuffix;if(this.fontPickerDiv=document.getElementById(n),!this.fontPickerDiv)throw Error('Missing div with id="'+n+'"');var i=document.createElement("button");i.classList.add("dropdown-button"),i.onclick=this.toggleExpanded,i.onkeypress=this.toggleExpanded,i.type="button",this.fontPickerDiv.appendChild(i),this.dropdownFamily=document.createElement("p"),this.dropdownFamily.textContent=this.fontManager.getActiveFont().family,this.dropdownFamily.classList.add("dropdown-font-family"),i.appendChild(this.dropdownFamily);var o=document.createElement("p");o.classList.add("dropdown-icon","loading"),i.appendChild(o),this.fontManager.init().then((function(n){var i=Array.from(n.values());"alphabet"===t&&i.sort((function(t,e){return t.family.localeCompare(e.family)})),e.generateFontList(i),o.classList.replace("loading","finished")})).catch((function(t){o.classList.replace("loading","error"),console.error("Error trying to fetch the list of available fonts"),console.error(t)}))},t.prototype.generateFontList=function(t){var e=this;this.ul=document.createElement("ul"),this.ul.classList.add("font-list"),t.forEach((function(t){e.addFontLi(t)})),this.fontPickerDiv.appendChild(this.ul);var n="font-button-"+a(this.fontManager.getActiveFont().family)+this.fontManager.selectorSuffix;this.activeFontButton=document.getElementById(n),this.activeFontButton?this.activeFontButton.classList.add("active-font"):console.error("Could not find font button with ID ("+n+")")},t.prototype.addFontLi=function(t,e){var n=this,i=a(t.family),o=document.createElement("li");o.classList.add("font-list-item");var s=document.createElement("button");s.type="button",s.id="font-button-"+i+this.fontManager.selectorSuffix,s.classList.add("font-button"),s.textContent=t.family;var r=function(){n.toggleExpanded(),n.setActiveFont(t.family)};s.onclick=r,s.onkeypress=r,o.appendChild(s),e?this.ul.insertBefore(o,this.ul.children[e]):this.ul.appendChild(o)},t.prototype.closeEventListener=function(t){for(var e=t.target,n=document.getElementById("font-picker"+this.fontManager.selectorSuffix);;){if(e===n)return;if(!e.parentNode)return void this.toggleExpanded();e=e.parentNode}},t.prototype.toggleExpanded=function(){this.expanded?(this.expanded=!1,this.fontPickerDiv.classList.remove("expanded"),document.removeEventListener("click",this.closeEventListener)):(this.expanded=!0,this.fontPickerDiv.classList.add("expanded"),document.addEventListener("click",this.closeEventListener))},t.prototype.getFonts=function(){return this.fontManager.getFonts()},t.prototype.addFont=function(t,e){if(Array.from(this.fontManager.getFonts().keys()).includes(t))throw Error('Did not add font to font picker: Font family "'+t+'" is already in the list');this.fontManager.addFont(t,!0);var n=this.fontManager.getFonts().get(t);n?this.addFontLi(n,e):console.error('Font "'+t+'" is missing in font list')},t.prototype.removeFont=function(t){this.fontManager.removeFont(t);var e=a(t),n=document.getElementById("font-button-"+e+this.fontManager.selectorSuffix);if(!n)throw Error('Could not remove font from font picker: Font family "'+t+'" is not in the list');var i=n.parentElement;n.remove(),i&&i.remove()},t.prototype.getActiveFont=function(){return this.fontManager.getActiveFont()},t.prototype.setActiveFont=function(t){this.fontManager.setActiveFont(t);var e=a(t);this.dropdownFamily.textContent=t,this.activeFontButton?(this.activeFontButton.classList.remove("active-font"),this.activeFontButton=document.getElementById("font-button-"+e+this.fontManager.selectorSuffix),this.activeFontButton.classList.add("active-font")):console.error("`activeFontButton` is undefined")},t.prototype.setOnChange=function(t){this.fontManager.setOnChange(t)},t}();e.a=F},24:function(t,e){!function(t,e,n,i){"use strict";var o=t.fn.twbsPagination,s=function(e,n){if(this.$element=t(e),this.options=t.extend({},t.fn.twbsPagination.defaults,n),this.options.startPage<1||this.options.startPage>this.options.totalPages)throw new Error("Start page option is incorrect");if(this.options.totalPages=parseInt(this.options.totalPages),isNaN(this.options.totalPages))throw new Error("Total pages option is not correct!");if(this.options.visiblePages=parseInt(this.options.visiblePages),isNaN(this.options.visiblePages))throw new Error("Visible pages option is not correct!");if(this.options.beforePageClick instanceof Function&&this.$element.first().on("beforePage",this.options.beforePageClick),this.options.onPageClick instanceof Function&&this.$element.first().on("page",this.options.onPageClick),this.options.hideOnlyOnePage&&1==this.options.totalPages)return this.options.initiateStartPageClick&&this.$element.trigger("page",1),this;if(this.options.href&&(this.options.startPage=this.getPageFromQueryString(),this.options.startPage||(this.options.startPage=1)),"UL"===("function"==typeof this.$element.prop?this.$element.prop("tagName"):this.$element.attr("tagName")))this.$listContainer=this.$element;else{var i=this.$element,o=t([]);i.each((function(e){var n=t("<ul></ul>");t(this).append(n),o.push(n[0])})),this.$listContainer=o,this.$element=o}return this.$listContainer.addClass(this.options.paginationClass),this.options.initiateStartPageClick?this.show(this.options.startPage):(this.currentPage=this.options.startPage,this.render(this.getPages(this.options.startPage)),this.setupEvents()),this};s.prototype={constructor:s,destroy:function(){return this.$element.empty(),this.$element.removeData("twbs-pagination"),this.$element.off("page"),this},show:function(t){if(t<1||t>this.options.totalPages)throw new Error("Page is incorrect.");this.currentPage=t,this.$element.trigger("beforePage",t);var e=this.getPages(t);return this.render(e),this.setupEvents(),this.$element.trigger("page",t),e},enable:function(){this.show(this.currentPage)},disable:function(){var e=this;this.$listContainer.off("click").on("click","li",(function(t){t.preventDefault()})),this.$listContainer.children().each((function(){t(this).hasClass(e.options.activeClass)||t(this).addClass(e.options.disabledClass)}))},buildListItems:function(t){var e=[];if(this.options.first&&e.push(this.buildItem("first",1)),this.options.prev){var n=t.currentPage>1?t.currentPage-1:this.options.loop?this.options.totalPages:1;e.push(this.buildItem("prev",n))}for(var i=0;i<t.numeric.length;i++)e.push(this.buildItem("page",t.numeric[i]));if(this.options.next){var o=t.currentPage<this.options.totalPages?t.currentPage+1:this.options.loop?1:this.options.totalPages;e.push(this.buildItem("next",o))}return this.options.last&&e.push(this.buildItem("last",this.options.totalPages)),e},buildItem:function(e,n){var i=t("<li></li>"),o=t("<a></a>"),s=this.options[e]?this.makeText(this.options[e],n):n;return i.addClass(this.options[e+"Class"]),i.data("page",n),i.data("page-type",e),i.append(o.attr("href",this.makeHref(n)).addClass(this.options.anchorClass).html(s)),i},getPages:function(t){var e=[],n=Math.floor(this.options.visiblePages/2),i=t-n+1-this.options.visiblePages%2,o=t+n,s=this.options.visiblePages;s>this.options.totalPages&&(s=this.options.totalPages),i<=0&&(i=1,o=s),o>this.options.totalPages&&(i=this.options.totalPages-s+1,o=this.options.totalPages);for(var a=i;a<=o;)e.push(a),a++;return{currentPage:t,numeric:e}},render:function(e){var n=this;this.$listContainer.children().remove();var i=this.buildListItems(e);t.each(i,(function(t,e){n.$listContainer.append(e)})),this.$listContainer.children().each((function(){var i=t(this);switch(i.data("page-type")){case"page":i.data("page")===e.currentPage&&i.addClass(n.options.activeClass);break;case"first":i.toggleClass(n.options.disabledClass,1===e.currentPage);break;case"last":i.toggleClass(n.options.disabledClass,e.currentPage===n.options.totalPages);break;case"prev":i.toggleClass(n.options.disabledClass,!n.options.loop&&1===e.currentPage);break;case"next":i.toggleClass(n.options.disabledClass,!n.options.loop&&e.currentPage===n.options.totalPages)}}))},setupEvents:function(){var e=this;this.$listContainer.off("click").on("click","li",(function(n){var i=t(this);if(i.hasClass(e.options.disabledClass)||i.hasClass(e.options.activeClass))return!1;!e.options.href&&n.preventDefault(),e.show(parseInt(i.data("page")))}))},changeTotalPages:function(t,e){return this.options.totalPages=t,this.show(e)},makeHref:function(t){return this.options.href?this.generateQueryString(t):"#"},makeText:function(t,e){return t.replace(this.options.pageVariable,e).replace(this.options.totalPagesVariable,this.options.totalPages)},getPageFromQueryString:function(t){var e=this.getSearchString(t),n=new RegExp(this.options.pageVariable+"(=([^&#]*)|&|#|$)").exec(e);return n&&n[2]?(n=decodeURIComponent(n[2]),n=parseInt(n),isNaN(n)?null:n):null},generateQueryString:function(t,e){var n=this.getSearchString(e),i=new RegExp(this.options.pageVariable+"=*[^&#]*");return n?"?"+n.replace(i,this.options.pageVariable+"="+t):""},getSearchString:function(t){var n=t||e.location.search;return""===n?null:(0===n.indexOf("?")&&(n=n.substr(1)),n)},getCurrentPage:function(){return this.currentPage},getTotalPages:function(){return this.options.totalPages}},t.fn.twbsPagination=function(e){var n,i=Array.prototype.slice.call(arguments,1),o=t(this),a=o.data("twbs-pagination"),r="object"==typeof e?e:{};return a||o.data("twbs-pagination",a=new s(this,r)),"string"==typeof e&&(n=a[e].apply(a,i)),undefined===n?o:n},t.fn.twbsPagination.defaults={totalPages:1,startPage:1,visiblePages:5,initiateStartPageClick:!0,hideOnlyOnePage:!1,href:!1,pageVariable:"{{page}}",totalPagesVariable:"{{total_pages}}",page:null,first:"First",prev:"Previous",next:"Next",last:"Last",loop:!1,beforePageClick:null,onPageClick:null,paginationClass:"pagination",nextClass:"page-item next",prevClass:"page-item prev",lastClass:"page-item last",firstClass:"page-item first",pageClass:"page-item",activeClass:"active",disabledClass:"disabled",anchorClass:"page-link"},t.fn.twbsPagination.Constructor=s,t.fn.twbsPagination.noConflict=function(){return t.fn.twbsPagination=o,this},t.fn.twbsPagination.version="1.4.2"}(window.jQuery,window,document)},46:function(t,e,n){var i,o,s;o=[n(47)],i=function(t){var e,n,i=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],o="onwheel"in document||document.documentMode>=9?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],s=Array.prototype.slice;if(t.event.fixHooks)for(var a=i.length;a;)t.event.fixHooks[i[--a]]=t.event.mouseHooks;var r=t.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var e=o.length;e;)this.addEventListener(o[--e],l,!1);else this.onmousewheel=l;t.data(this,"mousewheel-line-height",r.getLineHeight(this)),t.data(this,"mousewheel-page-height",r.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var e=o.length;e;)this.removeEventListener(o[--e],l,!1);else this.onmousewheel=null;t.removeData(this,"mousewheel-line-height"),t.removeData(this,"mousewheel-page-height")},getLineHeight:function(e){var n=t(e),i=n["offsetParent"in t.fn?"offsetParent":"parent"]();return i.length||(i=t("body")),parseInt(i.css("fontSize"),10)||parseInt(n.css("fontSize"),10)||16},getPageHeight:function(e){return t(e).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};function l(i){var o=i||window.event,a=s.call(arguments,1),l=0,p=0,h=0,f=0,u=0,g=0;if((i=t.event.fix(o)).type="mousewheel","detail"in o&&(h=-1*o.detail),"wheelDelta"in o&&(h=o.wheelDelta),"wheelDeltaY"in o&&(h=o.wheelDeltaY),"wheelDeltaX"in o&&(p=-1*o.wheelDeltaX),"axis"in o&&o.axis===o.HORIZONTAL_AXIS&&(p=-1*h,h=0),l=0===h?p:h,"deltaY"in o&&(l=h=-1*o.deltaY),"deltaX"in o&&(p=o.deltaX,0===h&&(l=-1*p)),0!==h||0!==p){if(1===o.deltaMode){var v=t.data(this,"mousewheel-line-height");l*=v,h*=v,p*=v}else if(2===o.deltaMode){var m=t.data(this,"mousewheel-page-height");l*=m,h*=m,p*=m}if(f=Math.max(Math.abs(h),Math.abs(p)),(!n||f<n)&&(n=f,d(o,f)&&(n/=40)),d(o,f)&&(l/=40,p/=40,h/=40),l=Math[l>=1?"floor":"ceil"](l/n),p=Math[p>=1?"floor":"ceil"](p/n),h=Math[h>=1?"floor":"ceil"](h/n),r.settings.normalizeOffset&&this.getBoundingClientRect){var b=this.getBoundingClientRect();u=i.clientX-b.left,g=i.clientY-b.top}return i.deltaX=p,i.deltaY=h,i.deltaFactor=n,i.offsetX=u,i.offsetY=g,i.deltaMode=0,a.unshift(i,l,p,h),e&&clearTimeout(e),e=setTimeout(c,200),(t.event.dispatch||t.event.handle).apply(this,a)}}function c(){n=null}function d(t,e){return r.settings.adjustOldDeltas&&"mousewheel"===t.type&&e%120==0}t.fn.extend({mousewheel:function(t){return t?this.bind("mousewheel",t):this.trigger("mousewheel")},unmousewheel:function(t){return this.unbind("mousewheel",t)}})},void 0===(s="function"==typeof i?i.apply(e,o):i)||(t.exports=s)}}]);