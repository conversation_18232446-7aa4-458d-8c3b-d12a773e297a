* {outline: 0;-webkit-box-sizing: border-box;-moz-box-sizing: border-box;box-sizing: border-box;margin: 0;padding: 0;touch-action: pan-y; }
html {font-size: 10px;-webkit-text-size-adjust: 100%;}
body {position: relative;padding: 0;margin: 0 auto;font-size: 10px;color: #000;background: #000;
    -webkit-text-size-adjust: none;
    -webkit-tap-highlight: rgba(0,0,0,0); position: relative;
}
input[type="button"],
input[type="submit"],
input[type="reset"] {-webkit-appearance: none;}
ul,ul li {list-style: none;}
table {border-collapse: collapse;border-spacing: 0}
table td {border-collapse: collapse;}
select, input, textarea {color: #333;border-radius: 0;-webkit-border-radius: 0;}
img{border: none;max-width: 100%;vertical-align: middle;}
a {color: #333;text-decoration: none;cursor: pointer;noline:-webkit-tap-highlight-color:rgba(0,0,0,0);/* åŽ»æŽ‰é“¾æŽ¥è§¦æ‘¸é«˜äº® */}
a:hover {text-decoration: none;}
a:focus {outline: none;-moz-outline: none;}
*{font-weight: normal;margin: 0;box-sizing: border-box;    padding: 0; }
img{border: none;max-width: 100%;width: 100%; vertical-align: middle;}
.auto{ margin:0 auto;}
.auto-1500{max-width: 1500px;margin: 0 auto;}
.clear {clear: both;height: 0px;overflow: hidden;zoom: 0;}
.clearfix:after { content:"\200B"; display:block; height:0; clear:both; }
input,input:focus,input:active{user-select: text;}
input::-webkit-input-placeholder {color: #ddcdbd;}
select, input{border:none;}
.gray{-webkit-filter: grayscale(100%); /* Chrome, Safari, Opera */filter: grayscale(100%);}
#menu li{list-style: none;}
#menu .active a { color: #ffdaaa;}
.wUp,.wzoom,.wfr,.wfi,.wfb{opacity: 0;}
.wUp.animated,.wzoom.animated,.wLeft.animated{opacity: 1;}
@font-face {
  font-family: 'roboto_bold';
  src: url("../font/Roboto-Bold.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'roboto_medium';
  src: url("../font/Roboto-Medium.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'roboto_light';
  src: url("../font/Roboto-Light.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
.f-roboto-light {
  font-family: 'roboto_light';
}
.f-roboto-medium {
  font-family: 'roboto_medium';
}
.f-roboto-bold {
  font-family: 'roboto_bold';
}
@font-face {
  font-family: 'SVN-Aleo';
  src: url("../font/SVN-Aleo Bold.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
.f-SVN-Aleo {
  font-family: 'SVN-Aleo';
}
.content-container5 {
  max-width: 970px;
  position: relative;
  margin: 0 auto;
}
.content-container4 {
  max-width: 1158px;
  position: relative;
  margin: 0 auto;
}
@media(max-width:1680px){
	.content-container5 {
	  max-width: 970px;
	}
	.content-container4 {
	  max-width: 1058px;
	}
}
@media(max-width:1366px){
	.content-container5 {
	  max-width: 670px;
	}
	.content-container4 {
	  max-width: 658px;
	}
}
@media(max-width:912px){
	.content-container5 {
	  max-width: 100%;
	}
}
.section{position: relative;overflow: hidden;}
.section.active{-webkit-animation: scaleA 1s ease both;}
.fp-section.fp-table.p2, .fp-slide.fp-table.p2{display: block;}
.banner-pic{opacity: 0;pointer-events: none;display: none;}
#section-one video{
	width:100%;
	height:100%;
	object-fit:cover;
	object-position:top center;
}

#section-one{background: url(../images/banner.jpg)no-repeat center top;background-size:cover;}
#section-two{background: url(../images/bg2.jpg)no-repeat center center;background-size:cover;z-index: 2;}
#section-three{background: url(../images/bg3.jpg)no-repeat top center;background-size:cover;z-index: 2;}
#section-four{background: url(../images/bg4.jpg)no-repeat center center;background-size: cover;z-index: 1;}
#section-five{background: url(../images/bg5.jpg)no-repeat top center;background-size: cover;}
#section-six{background: url(../images/bg6.jpg)no-repeat center center;background-size: cover;}
@media(max-width:912px){
	#section-one{background: url(../images/bannerinner1.jpg)no-repeat bottom center;background-size:cover;}
	#section-two{background: url(../images/bg2-w.jpg)no-repeat top center;background-size:cover;}
	#section-three{background: url(../images/bg3-w.jpg)no-repeat top center;background-size:cover;}
	#section-four{background: url(../images/bg4-w-2.jpg)no-repeat center center;background-size: cover;}
	#section-five{background: url(../images/bg5-w.jpg)no-repeat top center;background-size: cover;}
	#section-six{background: url(../images/bg6-w.jpg)no-repeat center center;background-size: cover;}
}
@media(max-width:850px){
	#section-four{background: url(../images/bg4-w.jpg)no-repeat center center;background-size: cover;}
}
@media(max-width:768px){
	#section-one{background: url(../images/bannerinner3.jpg)no-repeat bottom center;background-size:cover;}
	#section-four{background: url(../images/bg4-w-2.jpg)no-repeat center center;background-size: cover;}
}
@media(max-width:600px){
	#section-one{background: url(../images/bannerinner1.jpg)no-repeat bottom center;background-size:cover;}
}
@media(max-width:540px){
	#section-one{background: url(../images/bannerinner3.jpg)no-repeat bottom center;background-size:cover;}
	#section-three{background: url(../images/bg3-w-2.jpg)no-repeat top center;background-size:cover;}
	#section-four{background: url(../images/bg4-w-2.jpg)no-repeat center center;background-size: cover;}
}
@media(max-width:480px){
	#section-one{background: url(../images/bannerinner.jpg)no-repeat bottom center;background-size:cover;}
	#section-four{background: url(../images/bg4-w.jpg)no-repeat center center;background-size: cover;}
}
@media(max-width:375px){
	#section-one{background: url(../images/bannerinner1.jpg)no-repeat bottom center;background-size:cover;}
}
@media(max-width:320px){
	#section-one{background: url(../images/bannerinner1.jpg)no-repeat bottom center;background-size:cover;}
	#section-four{background: url(../images/bg4-w-2.jpg)no-repeat center center;background-size: cover;}
}
.clearfloat:after{display:block;clear:both;content:"";visibility:hidden;height:0}
.clearfloat{zoom:1}


@-webkit-keyframes scaleA {
    0% {
        opacity: 0;
        -webkit-transform: scale(1.4);
    }

    100% {
        opacity: 1;
        -webkit-transform: scale(1);
    }
}
.title{ margin: 0 auto;text-align: center;}
.pic-web{display: block;}
.pic-wap{display: none;}

.animated{-webkit-animation-duration: 0.5s;
    animation-duration: 0.5s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;}

@media(max-width:1750px){
	html,body{font-size:9px;}
	.btn-anniu{font-size: 2.2rem;line-height: 44px;}
}
@media(max-width:1600px){
	html,body{font-size:8.5px;}
}
@media(max-width:1550px){}
@media(max-width:820px){
html,body{font-size:10px;}
	.btn-anniu{font-size: 1.6rem;line-height: 34px;}
}
@media(max-width:912px){	
	.pic-web{display: none;}
    .pic-wap{display: block;}
}
@media(max-width:361px){
	html,body{font-size:9px;}
}
@media(max-width:321px){

}

/************** header ********************/
.foot-btn{width: 2%;position: absolute;bottom: 20px;left: 40px;z-index: 999;cursor: pointer;}
.head{position: fixed;top: 0;left: 0;width:15%;height: 100%;z-index: 10;}
.head:before{position: absolute;content: "";width: 1px;height: 100%;top: 0;left: 10%;background: rgba(144,136,126,0.3);}
.head .hanbager{display: none;cursor: pointer;position: relative;width: 55px;height: 55px;border: 2px rgba(244,235,209,0.4) solid; float: left;}
.head .hanbager div{position: absolute;width: 70%;height: 4px;background: #f4ebd1;top: 14px;left: 50%;transform: translate(-50%,0);}
.head .hanbager div:nth-child(2){top: 25px;}
.head .hanbager div:nth-child(3){top: 36px;}
.head .hanbager.open div{top: 23px;transform: rotate(45deg);-ms-transform: rotate(45deg);-moz-transform: rotate(45deg);-webkit-transform: rotate(45deg);
-o-transform: rotate(45deg);transform-origin: center center;left: 21%;}
.head .hanbager.open div:nth-child(2){display: none;}
.head .hanbager.open div:nth-child(3){transform: rotate(-45deg);-ms-transform: rotate(-45deg);-moz-transform: rotate(-45deg);top: 23px;}
.head .gobook{display: none;}
.head .logo{position: absolute;content: "";top: 0%;left: 4%;width: 53%;display: none}
.ic{position: absolute;top:1%;left:80%;width: 100%}
.ic a {float: left;margin: 0 11px 0 0}
.ic a:hover {float: left;margin: 0 11px 0 0;filter: brightness(1.3)}
.ic a img{max-height:49px; max-width:49px}
.head .head-right{left: 10%;position: absolute;top: 50%;transform: translate(0,-50%);}
.head .head-right li{position: relative;text-align: left;padding-left: 25px;margin:10% 0;}
.head .head-right li:after{position: absolute;content: "";width:14px;height:19px;background: url(../images/nav-0.png)no-repeat center center;background-size: 100% 100%;top: 50%;left: 0;transform: translate(0,-50%);}
.head .head-right li.jljl{display: none;}
.head .head-right li span{display: none;}
.head .head-right li.active{padding: 0;}
.head .head-right li.active:after{display: none;}
.head .head-right li.active a{display: none;}
.head .head-right li.active span{display: block;}
.head .head-right li.active span img{width:90%;}
.head .head-right li a{display: inline-block;font-size: 1.7rem;background: -webkit-linear-gradient(#e5e2d8, #eecf82);-webkit-background-clip: text;-webkit-text-fill-color: transparent;line-height: 28px;padding: 0 0;}
.head .head-right .foot-btn{display: none;width: 12%;bottom: 2%;}
.head .head-right .foot-btn img{min-width: 25px;}
@media(max-width:1700px){
	.head .head-right li:after{width:14px;height:19px;}
	.head .head-right li{padding-left: 25px;}
	.head .head-right li a{line-height:28px;}
	.head .head-right li.active span img{width:90%;}
}

@media(max-width:912px){
	.foot-btn{width:3%;bottom:0;right:10px;}
	.head{position: fixed;left:0;z-index:10;padding: 8px 0;width:100%;top:0;height:auto;background:rgba(0,0,0,0.5);}
	.head:before{display: none;}
	.head .hanbager{width:62px;height:62px;border: 1px rgba(244,235,209,0.4) solid;display: block;float: right;margin:0 4% 0 0;transform: scale(0.9);}
	.head .hanbager div{top:12px;width:60%;height:6px;}
	.head .hanbager div:nth-child(2){top:28px;}
    .head .hanbager div:nth-child(3){top:44px;}
	.head .hanbager.open div,.head .hanbager.open div:nth-child(3){top:25px;}
	
	.head .logo{top:4px;width:auto;width:54%;left:2%;display:block}
	.head .logo img{max-height:94px;max-width:94px;}
	.head .ic{width: 55%; top:7px;left: 21%;}
	.head .ic a {float: left;margin: 0 10px 0 0}
	.head .ic a:hover {float: left; margin: 0 10px 0 0;filter: brightness(1.3)}
	.head .ic a img{max-height:62px; max-width:62px}
	
	.head .head-right{width: 50%;position: fixed;content: "";height: 100%;top: 0; z-index: -1;transform: translate(0,0);left:100%;background: rgba(0,0,0,0.8);margin-right: 0;transition: all 0.6s ease;padding-left: 4%;padding-top: 23%;}
	.head .head-right:before{position: absolute;content: "";width: 1px;height: 100%;top: 0;left: 8%;background: rgba(144,136,126,0.3);}
	.head .head-right li{margin:14% 0;padding-left:35px;}
	.head .head-right li:after{width:24px;height:29px;}
	.head .head-right li.jljl{display:block;}
	.head .head-right li a{font-size:2rem;line-height:30px;padding: 0px 16px;}
	.head .head-right li.active span img{width:80%;}
	.foot-btn{display: none;}
	.head .head-right .foot-btn{display: block;}
	
}
@media(max-width:600px){
	.foot-btn{width:3%;bottom:0;right:10px;}
	.head{position: fixed;top:0;left:0;padding: 8px 0;width:100%;height:auto;background:rgba(0,0,0,0.5);}
	.head .hanbager{width:45px;height:45px;}
	.head .hanbager div{top:9px;width:60%;height:4px;}
	.head .hanbager div:nth-child(2){top:20px;}
    .head .hanbager div:nth-child(3){top:31px;}
	.head .hanbager.open div,.head .hanbager.open div:nth-child(3){top:20px;}
	.head .logo{top:4px;width:auto;width:54%;left:2%;display:block}
	.head .logo img{max-height:84px;max-width:84px;}
	
	.head .ic{width: 55%; top:7px;left: 21%;}
	.head .ic a {float: left;margin: 0 10px 0 0}
	.head .ic a:hover {float: left; margin: 0 10px 0 0;filter: brightness(1.3)}
	.head .ic a img{max-height:45px; max-width:45px}
	
	.head .head-right li{padding-left:20px;margin:12% 0;}
	.head .head-right li:after{width:14px;height:19px;}
	.head .head-right li.jljl{display:block;}
	.head .head-right li a{font-size: 1.6rem;line-height: 30px;padding: 0px 12px;}
	.head .head-right li.active span img{width:80%;}
}
@media(max-width:540px){
	.foot-btn{width: 3%;bottom: 0;right:10px;}
	.head{position: fixed;top: 0;left: 0;padding: 8px 0;width: 100%;height: auto;background: rgba(0,0,0,0.5);}
	.head:before{display: none;}
	.head .hanbager{width: 35px;height: 30px;border: 1px rgba(244,235,209,0.4) solid;display: block;float: right;margin:0 4% 0 0;transform: scale(0.9)}
	.head .hanbager div{top: 6px;width: 60%;height: 3px;}
	.head .hanbager div:nth-child(2){top: 12px;}
    .head .hanbager div:nth-child(3){top:18px;}
	.head .hanbager.open div,.head .hanbager.open div:nth-child(3){top:13px;}
	
	.head .logo{top: 4px;width: auto;width: 34%;left: 2%;display: block}
	.head .logo img{max-height: 54px; max-width: 54px;}
	.head .ic{width: 55%; top:7px;left: 21%;}
	.head .ic a {float: left;margin: 0 10px 0 0}
	.head .ic a:hover {float: left; margin: 0 10px 0 0;filter: brightness(1.3)}
	.head .ic a img{max-height: 32px; max-width: 32px}
	
	.head .head-right:before{position: absolute;content: "";width: 1px;height: 100%;top: 0;left: 8%;background: rgba(144,136,126,0.3);}
	.head .head-right li{padding-left:15px;margin: 12% 0;}
	.head .head-right li:after{width:14px;height:19px;}	
	.head .head-right li a{font-size: 1.4rem;line-height: 30px;padding: 0px 12px;}
	.head .head-right li.jljl{display: block;}
	.head .head-right li.active span img{width:80%;}
}
@media(max-width:320px){
	.head .head-right li a{font-size: 1.2rem;line-height: 30px;padding: 0px 12px;}
	.head .head-right li.active span img{width:90%;}
}
/************** frame 1 ********************/
.yyly{position: absolute;content: "";width: 41%;bottom:2%;right:30%; z-index: 3;text-align: center;}
.slogan{pointer-events: none;}
.yy-an{margin-top: -10%;}

.yy-an .down-box{width: 63%;margin: 0 auto;background: url(../images/down-bg.png)no-repeat center center;background-size: 100% 100%;padding:10px 30px 10px 45px; }
.yy-an .down-box li{display: block;float: left;width: 24%;margin-right: 1%;}
.yy-an .down-box li a:hover{filter: brightness(1.2)}
.yy-an .down-box li:nth-child(2){width: 46%; margin-right: 1%;}
.yy-an .down-box li:nth-child(2) a{display: block;float: left;width:100%;margin-bottom:3%;margin-top:3%;}
.yy-an .down-box li:nth-child(2) a:nth-child(2){margin-right:0%;}
.yy-an .down-box li:nth-child(3){width: 24%; margin-right: 0%;}

.video{position: absolute;top:44%;left:60%;width:100%;z-index: 4;}
.play {display: block;background-image: url("../images/play.png"); background-size: contain; width: 91px;height: 91px;font-size: 0}
.play:hover {filter: brightness(1.2);}
@media(max-width:1920px){
	.yyly{bottom:0%;}
}
@media(max-width:1600px){
	.yyly{bottom:3%;}
	.video{width:12%;top:50%;}
}
@media(max-width:912px){
	.yyly{width:100%;right: 0%;transform: translate(0,0);top:auto;bottom:59%;}
	.slogan{width:100%; position: absolute;animation: sloganAni .4s .7s linear both;}
	.slogan img {
		max-width: 100%; 
	    animation: blink 0.3s ease-in-out infinite;
	}
	.video{top:80%;left:46%;width:100%;}
}
@media(max-width:820px){
	.yyly{bottom:62%;}
	.video{top:80%;left:44%;width:100%;}
}
@media(max-width:768px){
	.yyly{bottom:62%;}
}
@media(max-width:600px){
	.yyly{bottom:58%;}
}
@media(max-width:540px){
	.yyly{bottom:63%;}
	.video{top:80%;left:44%;}
	.play {width:71px;height:71px;}
}
@media(max-width:480px){
	.yyly{bottom:58%;}
	.video{top:77%;left:43%;}
}
@media(max-width:431px){
	.yyly{bottom:52%;}
	.video{top:77%;left:42%;}
}
@media(max-width:415px){
	.yyly{bottom:52%;}
	.video{top:80%;left:44%;}
	.play {width:51px;height:51px;}
}
@media(max-width:376px){
	.yyly{bottom:58%;}
	.video{top:77%;left:43%;}
}
@media(max-width:360px){
	.yyly{bottom:52%;}
	.video{top:82%;left:43%;}
}
@media(max-width:320px){
	.yyly{bottom:62%;}
	.video{top:77%;left:42%;}
}
.main-height{opacity: 0;pointer-events: none;position: absolute;width:100%;top:0;left:0;}


/************** frame 2 ********************/
.sec2{position: absolute;width: 100%;height: 100%;top: 0;left: 0;overflow: hidden;}
.sec2-title{position: absolute;left: 35%;bottom:3%;z-index: 3;width:31%;text-align: center;}
@-webkit-keyframes sloganAni{0%{-webkit-transform:scale(1.8);opacity:0;}60%{-webkit-transform:scale(1);opacity:1;}65%{-webkit-transform:translate(-4px,-4px);}70%{-webkit-transform:translate(0,0);}75%{-webkit-transform:translate(4px,4px);}80%{-webkit-transform:translate(0,0);}85%{-webkit-transform:translate(-4px,4px);}90%{-webkit-transform:translate(0,0);}95%{-webkit-transform:translate(4px,-4px);}100%{-webkit-transform:translate(0,0);opacity:1;}}
@keyframes sloganAni{0%{transform:scale(1.8);opacity:0;}60%{transform:scale(1);opacity:1;}65%{transform:translate(-4px,-4px);}70%{transform:translate(0,0);}75%{transform:translate(4px,4px);}80%{transform:translate(0,0);}85%{transform:translate(-4px,4px);}90%{transform:translate(0,0);}95%{transform:translate(4px,-4px);}100%{transform:translate(0,0);opacity:1;}}
.sec2-stamp {
	position: absolute;right:36%;bottom:16%;z-index: 3;width:7%;animation: sloganAni .4s .7s linear both;
}
.sec2-stamp img {
		max-width: 100%; 
	    animation: blink 0.3s ease-in-out infinite;
	}
@keyframes blink {
    0% {
        opacity: 1;
		filter: brightness(1.1);
    }

    100% {
        opacity: 1;
    }
}
@media(max-width:912px){
	.sec2-title{left:20%;top:9%;bottom:0;width:60%;}
	.sec2-stamp{right:10%;bottom:16%;width:20%;}
}
@media(max-width:768px){
	.sec2-stamp{bottom:8%;}
}
@media(max-width:600px){
	.sec2-stamp{bottom:16%;}
}
@media (max-width:540px){
	.sec2-title{top:10%;}
	.sec2-stamp{bottom:8%;}
}
@media (max-width:480px){
	.sec2-title{top:8%;}
	.sec2-stamp{bottom:26%;}
}
@media (max-width:428px){
	
}
@media (max-width:415px){
	
}
@media (max-width:376px){
	
}
@media (max-width:320px){
	.sec2-title{top:11%;}
	.sec2-stamp{bottom:14%;}
	
}

/************** frame 3 ********************/
.sec3{position: absolute;width: 100%;height: 100%;top: 0;left: 0;overflow: hidden;}
.sec3-title{position: absolute;left:30%;bottom:3%;z-index: 3;width:40%;text-align: center;}

@media(max-width:912px){
	.sec3-title{left:0;bottom:10%;z-index: 3;width: 100%;}
}
@media (max-width:600px){
	.sec3-title{bottom:12%;}
}
@media (max-width:540px){
	.sec3-title{bottom:10%;}
}
@media (max-width:429px){
	.sec3-title{bottom:13%;}
}


/************** frame 4 ******************/
.sec4{position: absolute;width:100%;height: 100%;top:0;left:0;overflow: hidden;}
.sec4-title{position: absolute;left:25%;top:3%;z-index:3;width:35%;text-align: center;}
.sec4-thele{position: absolute;left:50%;top:30%;z-index:3;width:3%;}
.sec4-thele a:hover{filter: brightness(1.2)}
.sec4-chienngay{position: absolute;left:40%;bottom:3%;z-index:3;width:20%;}
.sec4-chienngay a:hover{filter: brightness(1.2)}
@media(max-width:912px){
	.sec4-title{left:15%;top:7%;width:70%;}
	.sec4-thele{left:32%;top:20%;width:8%;}
	.sec4-chienngay{left:28.5%;bottom:10%;width:43%;}
}
@media(max-width:850px){
	.sec4-thele{left:25%;top:20%;width:8%;}
	.sec4-chienngay{left:28.5%;bottom:8%;width:43%;}
}
@media(max-width:540px){
	.sec4-title{;top:8%;}
}
@media(max-width:480px){
	.sec4-title{;top:10%;}
}
@media(max-width:415px){
	.sec4-title{;top:12%;}
}
@media(max-width:320px){
	.sec4-thele{left:25%;top:23%;width:8%;}
}

/************** frame 5 *******************/
.sec5{position: absolute;width: 100%;height: 100%;top: 0;left: 0;overflow: hidden;}
.sec5-title{position: absolute;left: 25%;top:3%;z-index: 3;width: 50%;text-align: center;}
.slide-frame5{position: absolute;left:19%;bottom:7%;z-index: 3;width:60%;}

.class_swiper{width:100%; height:674px;}
.class_swiper .swiper-slide{ width:100%; height:942px;}
.nhanvat{margin:2% 0 0 0; }
.class_opa_box{width:100%; height:100%; z-index:4;margin:13% 0 0 0;background:url("../images/class/genaral-tab.png") no-repeat center center; background-size:50%}
.class_tab{width:100%; display: flex; flex-wrap: wrap; justify-content: center;}
.class_tab li{width:102px; height:136px; margin:1% 1% 0 1%; cursor:pointer;}
#class_tab_nguy{background:url("../images/class/ic-nguy.png") no-repeat; background-size: 100%}
#class_tab_nguy.on{background:url("../images/class/ic-nguy-hover.png") no-repeat;background-size: 100%}
#class_tab_thuc{background:url("../images/class/ic-thuc.png") no-repeat;background-size: 100%}
#class_tab_thuc.on{background:url("../images/class/ic-thuc-hover.png") no-repeat;background-size: 100%}
#class_tab_ngo{background:url("../images/class/ic-ngo.png") no-repeat;background-size: 100%}
#class_tab_ngo.on{background:url("../images/class/ic-ngo-hover.png") no-repeat;background-size: 100%}
#class_tab_quan{background:url("../images/class/ic-quan.png") no-repeat;background-size: 100%}
#class_tab_quan.on{background:url("../images/class/ic-quan-hover.png") no-repeat;background-size: 100%}
.sec5-bn-genaral{position: absolute;left:40%;bottom:3%;z-index:3;width:20%;}
.sec5-bn-genaral a:hover{filter: brightness(1.2)}
@media(max-width:912px){
	.sec5-title{left: 0%;top:11%;width: 100%;}
	.slide-frame5{left:0;bottom:20%;width:100%;}
	.class_opa_box{margin:13% 0 0 0;background-size:75% 100%}
	.class_tab li{width:102px; height:136px;margin:1% 3% 0 3%;}
	.sec5-bn-genaral{left:28.5%;bottom:10%;width:43%;}
}
@media(max-width:820px){
	.sec5-title{top:11%;}
	.slide-frame5{bottom:16%;}
	.class_opa_box{margin:13% 0 0 0;background-size:70% 100%}
	.class_tab li{width:72px; height:86px;margin:1% 3% 0 3%;}
	.sec5-bn-genaral{bottom:10%;}
}
@media(max-width:768px){
	.sec5-title{top:10%;}
	.slide-frame5{bottom:7%;}
	.sec5-bn-genaral{bottom:9%;}
}
@media(max-width:600px){
	.sec5-title{top:11%;}
	.slide-frame5{bottom:0%;}
	.class_tab li{width:57px; height:76px;margin:1% 3.5% 0 3.5%;}
	.sec5-bn-genaral{bottom:10%;}
	.nhanvat{margin:3% 0 0 0; }
}
@media only screen and (max-width : 540px) {
	.sec5-title{top:9%;}
	.slide-frame5{bottom:-22%;}
	.nhanvat{margin:1% 0 0 0; }
}
@media only screen and (max-width :480px) {
	.sec5-title{top:11%;}
	.slide-frame5{bottom:-10%;}
	.class_tab li{width:47px; height:66px;margin:2% 3.5% 0 3.5%;}
	.nhanvat{margin:5% 0 0 0; }
}
@media only screen and (max-width :451px) {
	.slide-frame5{bottom:-17%;}
	.class_tab li{width:40px; height:59px;margin:2% 3.5% 0 3.5%;}
	.nhanvat{margin:5% 0 0 0; }
}
@media only screen and (max-width :431px) {
	.slide-frame5{bottom:-10%;}
}
@media only screen and (max-width :415px) {
	.slide-frame5{bottom:-23%;}
}
@media only screen and (max-width :376px) {
	.slide-frame5{bottom:-37%;}
}
@media only screen and (max-width :360px) {
	.slide-frame5{bottom:-36%;}
}
@media only screen and (max-width :320px) {
	.slide-frame5{bottom:-70%;}
	.class_tab li{width:30px; height:45px;margin:2% 3.5% 0 3.5%;}
	.nhanvat{margin:3% 0 0 0; }
}
.center-align{
    margin: 0 auto 0 auto;
}
/*
.class_swiper{width:100%; height:882px; margin-left:0; margin-top:24%;}
.class_swiper .swiper-slide{ width:100%; height:942px;}

.nhanvat{ margin:0 0 0 0; }

.class_opa_box{width:100%; height:auto; z-index:4;margin:13% 0 0 0}
.class_tab{width:100%; display: flex; flex-wrap: wrap; justify-content: center;}
.class_tab li{width:80px; height:60px; margin-right:-10%; cursor:pointer;}

#class_tab_tinhtuc{ background:url("../images/class/tab-tinhtuc.png") no-repeat; background-size: 50%}
#class_tab_tinhtuc.on{ background:url("../images/class/tab-tinhtuc-hover.png") no-repeat;background-size: 50%}
#class_tab_caibang{ background:url("../images/class/tab-caibang.png") no-repeat;background-size: 50%}
#class_tab_caibang.on{ background:url("../images/class/tab-caibang-hover.png") no-repeat;background-size: 50%}
#class_tab_minhgiao{ background:url("../images/class/tab-minhgiao.png") no-repeat;background-size: 50%}
#class_tab_minhgiao.on{ background:url("../images/class/tab-minhgiao-hover.png") no-repeat;background-size: 50%}
#class_tab_ngamy{ background:url("../images/class/tab-ngamy.png") no-repeat;background-size: 50%}
#class_tab_ngamy.on{ background:url("../images/class/tab-ngamy-hover.png") no-repeat;background-size: 50%}
#class_tab_thienlong{ background:url("../images/class/tab-thienlong.png") no-repeat;background-size: 50%}
#class_tab_thienlong.on{ background:url("../images/class/tab-thienlong-hover.png") no-repeat;background-size: 50%}
#class_tab_thienson{ background:url("../images/class/tab-thienson.png") no-repeat;background-size: 50%}
#class_tab_thienson.on{ background:url("../images/class/tab-thienson-hover.png") no-repeat;background-size: 50%}
#class_tab_thieulam{ background:url("../images/class/tab-thieulam.png") no-repeat;background-size: 50%}
#class_tab_thieulam.on{ background:url("../images/class/tab-thieulam-hover.png") no-repeat;background-size: 50%}
#class_tab_tieudao{ background:url("../images/class/tab-tieudao.png") no-repeat;background-size: 50%}
#class_tab_tieudao.on{ background:url("../images/class/tab-tieudao-hover.png") no-repeat;background-size: 50%}
#class_tab_vodang{ background:url("../images/class/tab-vodang.png") no-repeat;background-size: 50%}
#class_tab_vodang.on{ background:url("../images/class/tab-vodang-hover.png") no-repeat;background-size: 50%}

@media only screen and (min-width : 360px) {
	.class-text{font-size: 12px;}
	.class_tab li{width:85px; height:65px; margin-right:-8%;}
}
@media only screen and (min-width : 414px) {
	.class-text{font-size: 13px;}
	.class_tab li{width:95px; height:75px; margin-right:-8%;}
}
@media only screen and (min-width : 480px) {
	.class-text{font-size: 15px;}
	.class_tab li{width:115px; height:85px; margin-right:-8%;}
}
@media only screen and (min-width : 540px) {
	.class-text{font-size: 18px;}
	.class_tab li{width:93px; height:85px; margin-right:1%; margin-bottom: 3%}
	#class_tab_tinhtuc{background-size:80%}
	#class_tab_tinhtuc.on{background-size: 80%}
	#class_tab_caibang{background-size: 80%}
	#class_tab_caibang.on{background-size: 80%}
	#class_tab_minhgiao{background-size: 80%}
	#class_tab_minhgiao.on{background-size: 80%}
	#class_tab_ngamy{background-size: 80%}
	#class_tab_ngamy.on{background-size: 80%}
	#class_tab_thienlong{background-size: 80%}
	#class_tab_thienlong.on{background-size: 80%}
	#class_tab_thienson{background-size: 80%}
	#class_tab_thienson.on{background-size: 80%}
	#class_tab_thieulam{background-size: 80%}
	#class_tab_thieulam.on{background-size: 80%}
	#class_tab_tieudao{background-size: 80%}
	#class_tab_tieudao.on{background-size: 80%}
	#class_tab_vodang{background-size: 80%}
	#class_tab_vodang.on{background-size: 80%}
}
@media only screen and (min-width : 768px) {

	.class-text{font-size: 25px;}
	.class_tab li{width:93px; height:110px; margin-right:5%; margin-bottom: 3%}
	#class_tab_tinhtuc{background-size: 100%}
	#class_tab_tinhtuc.on{background-size: 100%}
	#class_tab_caibang{background-size: 100%}
	#class_tab_caibang.on{background-size: 100%}
	#class_tab_minhgiao{background-size: 100%}
	#class_tab_minhgiao.on{background-size: 100%}
	#class_tab_ngamy{background-size: 100%}
	#class_tab_ngamy.on{background-size: 100%}
	#class_tab_thienlong{background-size: 100%}
	#class_tab_thienlong.on{background-size: 100%}
	#class_tab_thienson{background-size: 100%}
	#class_tab_thienson.on{background-size: 100%}
	#class_tab_thieulam{background-size: 100%}
	#class_tab_thieulam.on{background-size: 100%}
	#class_tab_tieudao{background-size: 100%}
	#class_tab_tieudao.on{background-size: 100%}
	#class_tab_vodang{background-size: 100%}
	#class_tab_vodang.on{background-size: 100%}

	
	
}
@media only screen and (min-width : 992px) {
	.class_swiper{ width:2000px; height:1084px; left:50%;}
	.class_swiper .swiper-slide{width:2000px; height:942px;}

	.nhanvat{ margin:0 0 0 20%; }

	.class_opa_box{width:100%; height:auto; z-index:4;margin: 18% 0 0 0}
	.class_tab{width:100%; height:110px; display: flex; flex-direction: row; justify-content: center;}
	.class_tab li{width:93px; height:110px; margin: 0 2% 0 0; cursor:pointer;}

}
@media only screen and (min-width : 1200px) {
	.class_swiper{width:2000px; height:1084px; left:50%; margin-left:-1000px; margin-top:23%;}
	.class_opa_box{width:100%; height:auto; z-index:4;margin: 13% 0 0 0}
}
@media only screen and (min-width : 1600px) {
	.class_swiper{width:2000px; height:1084px; left:50%; margin-left:-1000px; margin-top:20%;}
	.class_opa_box{width:100%; height:auto; z-index:4;margin: 11% 0 0 0}
}
@media only screen and (min-width : 1920px) {
	.class_swiper{width:2000px; height:1084px; left:50%; margin-left:-1000px; margin-top:16%;}
	.class_opa_box{width:100%; height:auto; z-index:4;margin: 9% 0 0 0}
}

/************** frame 6 ********************/
.sec6{position: absolute;width: 100%;height: 100%;top: 0;left: 0;overflow: hidden;}
.sec6-title{position: absolute;left: 25%;top:5%;z-index: 3;width: 50%;text-align: center;}
.slide-frame6{position: absolute;left:15%;bottom:75%;z-index: 3;width:70%;}
/* slide server pc*/
.features__wrapper{position:relative}
.features__carousel{position:absolute;width:100%;left:0;top:0}
.features__carousel .swiper-container .swiper-wrapper .swiper-slide,.features__carousel .swiper-container .swiper-wrapper .swiper-slide img{width:1038px;height:583px}
.features__carousel .swiper-container .swiper-wrapper .swiper-slide img{transition:all .4s}
.features__carousel .swiper-container .swiper-wrapper .swiper-slide-next>img{opacity:.8;filter:brightness(20%)}
.features__carousel .swiper-container .swiper-wrapper .swiper-slide-prev>img{opacity:.8;filter:brightness(20%)}
.features__carousel .swiper-pagination{width:100%;bottom:-50px}
.features__carousel .swiper-pagination-bullet{width:34px;height:34px;background:url(../images/slideimg/slide_normal.png) no-repeat; background-size: contain;margin:0 5px;}
.features__carousel .swiper-pagination-bullet-active{background:url(../images/slideimg/slide_active.png) no-repeat ;background-size: contain;}
.features__carousel .swiper-button-next{background:none; right:60px}
.features__carousel .swiper-button-next:after{display:block}
.features__carousel .swiper-button-next .slide-next{background:url(../images/slideimg/slide_next.png)  no-repeat; background-size: contain;width:62px;height:65px;}
.features__carousel .swiper-button-prev{background:none; left:0}
.features__carousel .swiper-button-prev:after{display:none}
.features__carousel .swiper-button-prev .slide-prev{background:url(../images/slideimg/slide_pre.png)  no-repeat;background-size: contain;width:62px;height:65px}
/* slide server mobile*/
@media only screen and (max-width: 912px) {
	.sec6-title{left:2%;top:15%;width:100%;}
	.slide-frame6{left:5%;bottom:75%;width:90%;}
	.features__carousel .swiper-container .swiper-wrapper .swiper-slide,.features__carousel .swiper-container .swiper-wrapper .swiper-slide img{width:100%;height:auto}
	.features__carousel .swiper-button-next{background:none; right:0px}
	.features__carousel .swiper-button-next .slide-next{width:62px;height:65px;}
	.features__carousel .swiper-button-prev{background:none; left:-40px}
	.features__carousel .swiper-button-prev .slide-prev{width:62px;height:65px}
}
@media only screen and (max-width:600px) {
	.features__carousel .swiper-button-next .slide-next{width:62px;height:65px;}
	.features__carousel .swiper-button-prev{background:none; left:-30px}
	.features__carousel .swiper-button-prev .slide-prev{width:62px;height:65px}
}
@media only screen and (max-width:540px) {
	.features__carousel .swiper-pagination-bullet{width:24px;height:24px;}
	.features__carousel .swiper-button-next{background:none; right:-10px}
	.features__carousel .swiper-button-next .slide-next{width:42px;height:45px;}
	.features__carousel .swiper-button-prev{background:none; left:-25px}
	.features__carousel .swiper-button-prev .slide-prev{width:42px;height:45px}
}
@media only screen and (max-width:431px) {
	.features__carousel .swiper-pagination-bullet{width:20px;height:20px;}
	.features__carousel .swiper-button-next{background:none; right:-20px}
	.features__carousel .swiper-button-next .slide-next{width:32px;height:35px;}
	.features__carousel .swiper-button-prev{background:none; left:-25px}
	.features__carousel .swiper-button-prev .slide-prev{width:32px;height:35px}
}
/***************** FOOTER ****************/

.foot{position: fixed;width: 100%;z-index: 9;background: url("../images/footer.png")no-repeat top center;background-size:cover;bottom: -80%;transition: all 1s ease; display: flex; flex-direction: row; justify-content: center; margin: 0 auto; padding:3% 0 1% 20%;}
.foot.active{bottom: 0;}
.foot img{max-height: 69px;}
.foot-left{ width: 16%; margin:1% 0 0 0}
.foot-left a{display: inline-block;margin: 0 5px;width: 217px;height: 69px;background: url(../images/vtc.png)no-repeat;background-size: 100% 100%;}
.foot-right{width: 50%; color: #ffffff;font-size:12px; line-height: 18px; text-align: left}
.foot-right a{color: #fee791;}
.foot-right a:hover{color: #d8ad0c;}
@media(max-width:912px){
	.foot{bottom: -80%;margin: 0 auto; padding:3% 0 1% 0;}
	.foot-left{ width: 26%; margin:1% 0 0 0}	
}
@media(max-width:820px){
	.foot-left{margin:3% 0 0 0}
	.foot-left a{margin: 0 5px;width: 217px;height: 69px;background-size: 80% 80%;}
}
@media(max-width:600px){
	.foot{flex-direction: column;padding: 1% 0 1% 0;}
	.foot-left{width: 100%;margin:0% 0 0 0; text-align: center;}
	.foot-left a{margin: 0 0 0 5%;width: 180px;height: 59px;}
	.foot-right{width: 100%;text-align: center}
}
@media(max-width:540px){
	.foot{flex-direction: column;padding:3% 0 1% 0;}
	.foot-left a{width: 137px;height: 49px;}
	.foot-right{font-size:11px; line-height: 14px; }
}
@media(max-width:415px){
	.foot-right{font-size:10px; line-height: 13px; }
}
@media(max-width:376px){
	.foot-left a{margin: 0 0 0 5%;width: 130px;height: 43px;}
}
@media(max-width:320px){
	.foot-right{font-size:9px; line-height: 12px; }
}



.nav-bot{display: none;position: fixed;width: 100%;left: 0;bottom: 0;padding: 5px 0; background: rgba(0,0,0,0.5);}
.nav-bot a{width: 33%;margin-right: 0.5%; float: left;text-align: center;}
.nav-bot .store-btn a span{font-size: 1.2rem;}
.nav-bot .top{position: absolute;bottom: 100%;right: 26%;width: 40px;}

@media(max-width:912px){
	.nav-bot a.fc-fb{margin-right: 0;}
    .nav-bot{display: block;}
	.fc .fc-fb{position: absolute;right: 85%;bottom: 0;display: none;}
	.fc .fc-fb:after{display: none;}
}
@media(max-width:820px){
	.fc{width: 12%;transform: translate(0,-23%);}	
}
@media(max-width:540px){
	.nav-bot a.fc-fb{margin-right: 0;}
    .nav-bot{display: block;}
}
@-webkit-keyframes b { 0% {-o-transform: rotate(0deg);-webkit-transform: rotate(0deg);-moz-transform: rotate(0deg);-ms-transform: rotate(0deg);transform: rotate(0deg);}
    to { -o-transform: rotate(1turn); -webkit-transform: rotate(1turn); -moz-transform: rotate(1turn); -ms-transform: rotate(1turn); transform: rotate(1turn); }}
@-moz-keyframes b {  0% {-o-transform: rotate(0deg);-webkit-transform: rotate(0deg);-moz-transform: rotate(0deg);-ms-transform: rotate(0deg);transform: rotate(0deg);}
    to { -o-transform: rotate(1turn); -webkit-transform: rotate(1turn); -moz-transform: rotate(1turn); -ms-transform: rotate(1turn); transform: rotate(1turn); }}
@-o-keyframes b {  0% {-o-transform: rotate(0deg);-webkit-transform: rotate(0deg);-moz-transform: rotate(0deg);-ms-transform: rotate(0deg);transform: rotate(0deg)}
    to { -o-transform: rotate(1turn); -webkit-transform: rotate(1turn); -moz-transform: rotate(1turn); -ms-transform: rotate(1turn); transform: rotate(1turn) }}
@-ms-keyframes b {  0% {-o-transform: rotate(0deg);-webkit-transform: rotate(0deg);-moz-transform: rotate(0deg);-ms-transform: rotate(0deg);
    transform: rotate(0deg)}
    to { -o-transform: rotate(1turn); -webkit-transform: rotate(1turn); -moz-transform: rotate(1turn); -ms-transform: rotate(1turn); transform: rotate(1turn) }}
@keyframes b {  0% {-o-transform: rotate(0deg);-webkit-transform: rotate(0deg);-moz-transform: rotate(0deg);-ms-transform: rotate(0deg);transform: rotate(0deg)}
    to { -o-transform: rotate(1turn); -webkit-transform: rotate(1turn); -moz-transform: rotate(1turn); -ms-transform: rotate(1turn); transform: rotate(1turn) }}

@-webkit-keyframes b1 { 0% {-o-transform: rotate(0deg) translate(-50%,-50%);-webkit-transform: rotate(0deg) translate(-50%,-50%);-moz-transform: rotate(0deg) translate(-50%,-50%);-ms-transform: rotate(0deg) translate(-50%,-50%);transform: rotate(0deg) translate(-50%,-50%);}
    to { -o-transform: rotate(1turn) translate(-50%,-50%); -webkit-transform: rotate(1turn) translate(-50%,-50%); -moz-transform: rotate(1turn) translate(-50%,-50%); -ms-transform: rotate(1turn) translate(-50%,-50%); transform: rotate(1turn) translate(-50%,-50%); }}

@keyframes sg { from {left:0;}
    to {left:100%;}}
@-webkit-keyframes sg /* Safari ÃƒÂ¥Ã¢â‚¬â„¢Ã…â€™ Chrome */ { from {left:0;}
    to {left:100%;}}
@-moz-keyframes sg /* Firefox */ { from {left:0;}
    to {left:100%;}}
@-o-keyframes sg /* Opera */ { from {left:0;}
    to {left:100%;}}


@keyframes zt { 0% {transform: scale(0.87);}
                   50% { transform: scale(1);  }
    100% {transform: scale(0.87);}}
@-webkit-keyframes zt /* Safari ÃƒÂ¥Ã¢â‚¬â„¢Ã…â€™ Chrome */ { 0% {-webkit-transform: scale(0.87);}
                   50% { -webkit-transform: scale(1); }
    100% {-webkit-transform: scale(0.87);}}
@-moz-keyframes zt /* Firefox */ { 0% {-moz-transform: scale(0.87);}
                   50% { -moz-transform: scale(1); }
    100% {-moz-transform: scale(0.87);}}
@-o-keyframes zt /* Opera */ { 0% {-o-transform: scale(0.87);}
                   50% { -o-transform: scale(1); } 
    100% {-o-transform: scale(0.87);}}


@keyframes zt1 { 0% {transform: scale(0.87);}
                   50% { transform: scale(1);  }
    100% {transform: scale(0.87);}}
@-webkit-keyframes zt1 /* Safari ÃƒÂ¥Ã¢â‚¬â„¢Ã…â€™ Chrome */ { 0% {-webkit-transform: scale(0.87);}
                   50% { -webkit-transform: scale(1); }
    100% {-webkit-transform: scale(0.87);}}
@-moz-keyframes zt1 /* Firefox */ { 0% {-moz-transform: scale(0.87);}
                   50% { -moz-transform: scale(1); }
    100% {-moz-transform: scale(0.87);}}
@-o-keyframes zt1 /* Opera */ { 0% {-o-transform: scale(0.87);}
                   50% { -o-transform: scale(1); } 
    100% {-o-transform: scale(0.87);}}



@-webkit-keyframes error-swing {
    0% {
        -webkit-transform: rotate(1deg)
    }

    100% {
        -webkit-transform: rotate(-2deg)
    }
}

@-moz-keyframes error-swing {
    0% {
        -moz-transform: rotate(1deg)
    }

    100% {
        -moz-transform: rotate(-2deg)
    }
}

@keyframes error-swing {
    0% {
        transform: rotate(1deg)
    }

    100% {
        transform: rotate(-2deg)
    }
}

@keyframes zt2 { 0% {transform: scale(1.15) translate(-50%,0);transform-origin:0 50%;}
                   50% { transform: scale(1) translate(-50%,0); transform-origin:0 50%; }
    100% {transform: scale(1.15) translate(-50%,0);transform-origin:0 50%;}}
@-webkit-keyframes zt2 /* Safari ÃƒÂ¥Ã¢â‚¬â„¢Ã…â€™ Chrome */ { 0% {-webkit-transform: scale(1.15) translate(-50%,0);-webkit-transform-origin:0 50%;}
                   50% { -webkit-transform: scale(1) translate(-50%,0);-webkit-transform-origin:0 50%; }
    100% {-webkit-transform: scale(1.15) translate(-50%,0);-webkit-transform-origin:0 50%;}}
@-moz-keyframes zt2 /* Firefox */ { 0% {-moz-transform: scale(1.15) translate(-50%,0);-moz-transform-origin:0 50%;}
                   50% { -moz-transform: scale(1) translate(-50%,0);-moz-transform-origin:0 50%; }
    100% {-moz-transform: scale(1.15) translate(-50%,0);-moz-transform-origin:0 50%;}}
@-o-keyframes zt2 /* Opera */ { 0% {-o-transform: scale(1.15) translate(-50%,0);-o-transform-origin:0 50%;}
                   50% { -o-transform: scale(1) translate(-50%,0); -o-transform-origin:0 50%;} 
    100% {-o-transform: scale(1.15) translate(-50%,0);-o-transform-origin:0 50%;}}

.fadeBig {-webkit-animation-name: fadeBig;animation-name: fadeBig;}
@keyframes fadeBig{ 0% {transform: scale(1.55);-webkit-transform: scale(1.55);opacity: 0;}
	100% {transform: scale(1);-webkit-transform: scale(1);opacity: 1;}
}

@keyframes fadebigsmall{ 0% {opacity: 0;}
	50% {opacity: 1;}
	100% {opacity: 0;}
}
.fadeIn{-webkit-animation-name:fadeIn;animation-name:fadeIn}
@-webkit-keyframes fadeIn{0%{opacity:0}100%{opacity:1}}
@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}

.fadeInRight{-webkit-animation-name:fadeInRight;animation-name:fadeInRight}
@-webkit-keyframes fadeInRight{0%{opacity:0;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}100%{opacity:1;-webkit-transform:none;transform:none}}
@keyframes fadeInRight{0%{opacity:0;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}100%{opacity:1;-webkit-transform:none;transform:none}}


.an-de-1{
	animation-delay:0.3s;
}


/* ----------------------------------- MENU RIGHT ---------------------------------------*/

@keyframes anim2{0%,100%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}
10%,30%,50%,70%,90%{-webkit-transform:translateX(-10px);-ms-transform:translateX(-10px);transform:translateX(-10px)}
20%,40%,60%,80%{-webkit-transform:translateX(10px);-ms-transform:translateX(10px);transform:translateX(10px)}}
@-webkit-keyframes anim2{0%,100%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}
10%,30%,50%,70%,90%{-webkit-transform:translateX(-10px);-ms-transform:translateX(-10px);transform:translateX(-10px)}
20%,40%,60%,80%{-webkit-transform:translateX(10px);-ms-transform:translateX(10px);transform:translateX(10px)}}
@keyframes anim4{from{transform:scale(1,1);opacity:0.5;}to{transform:scale(1.8,1.8);opacity:0;}}
@-webkit-keyframes anim4{from{transform:scale(1,1);opacity:0.5;}to{transform:scale(1.8,1.8);opacity:0;}}
@keyframes wts {
  0% {
    top: 7px;
  }
  100% {
    top: 70%;
  }
}
.nav_right {
  display: block;
  position: fixed;
  z-index: 999;
  right: -160px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width:189px;
  height:567px;
  background-image: url(../images/sprite.png);
  background-position: 0 0px;
  -webkit-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
.nav_right.open {
  right: 0;
}
@media (max-width: 1024px) {
  .nav_right {
    display: none;
  }
}
.nav_right .qr_code span {
  position: relative;
  display: block;
  margin:151px auto 0 40px;
  width: 120px;
  height: 120px;
}
.nav_right .qr_code span i {
  position: absolute;
  left: 1px;
  top: 10px;
  width: 116px;
  height: 8px;
  background: url(../images/wt.png) no-repeat;
  animation: wts 1s infinite alternate linear;
}
.nav_right .qr_code span img {
  position: absolute;
  top: 0;
  left: 0;
}
.nav_right ul.buttai {
  width: 143px;
  height: auto;
  position: relative;
  margin:5px 0 0 28px;
}
.nav_right ul.buttai li {
  float: left;
  margin:15px 0 0 0;
}
.nav_right ul.buttai .app-info__install--pc > a {
  display: block;
  text-indent: -9999px;
}
.nav_right ul.buttai .app-info__install--pc > a {
  height: 40px;
  width: 143px;
}
.button-appstore a {
  background-image: url("../images/sprite.png") !important;
  background-position: -189px -40px !important;
}
.button-appstore:hover a {
  background-image: url("../images/sprite.png") !important;
  background-position: -189px 0 !important;
}
.button-googleplay a {
  background-image: url("../images/sprite.png") !important;
  background-position: -189px -120px !important;
}
.button-googleplay:hover a {
  background-image: url("../images/sprite.png") !important;
  background-position: -189px -80px !important;
}
.button-apk a {
  background-image: url("../images/sprite.png") !important;
  background-position: -189px -200px !important;
}
.button-apk:hover a {
  background-image: url("../images/sprite.png") !important;
  background-position: -189px -160px !important;
}
.button-nox a {
  background-image: url("../images/sprite.png") !important;
  background-position: -250px -266px !important;
}
.button-nox:hover a {
  background-image: url("../images/sprite.png") !important;
  background-position: -250px -228px !important;
}
.nav_right  .gift_code{position:relative;width:151px;height:120px;animation:anim2 3s ease-in-out infinite alternate;display:block;background:url("../images/giftcode.png")no-repeat bottom;top:130px;left:25px;}
.nav_right .gift_code:after{position:absolute;content:'';width:100%;height:100%;left:0;top:0;right:0;bottom:0;margin:auto;background:url("../images/giftcode.png")no-repeat bottom;opacity:0;}
.nav_right .gift_code:hover:after{animation:anim4 0.6s ease-in-out;}

.nav_right .i_control {
  width:42px;
  height:60px;
  background: url(../images/i_close_nav_right.png) no-repeat center center;
  background-size: 100%;
  position: absolute;
  top:280px;
  left:-17px;
  cursor: pointer;
}
.nav_right .i_control.i_control_active {
  left:-17px;
  background: url(../images/i_open_nav_right.png) no-repeat center center;
}


/*==============================POPUP===================================*/
.popup {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: none;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 999;
}
.popup .content-popup {
  left: 50%;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.popup .wrapper-popup2 {
  min-width: 320px;
  width: 100%;
  height: 230px;
  background: url(../images/pop-m.png) no-repeat center center;
  background-size: 100% 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.popup .wrapper-popup3 {
  min-width: 320px;
  width: 100%;
  height:420px;
  background: url(../images/pop2-m.png) no-repeat center center;
  background-size: 100% 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

@media only screen and (min-width: 360px) {
	.popup .wrapper-popup2{
		min-width: 360px;
		height: 250px;
	}
	.popup .wrapper-popup3 {
		min-width: 360px;
		height: 460px;
	}
}
@media only screen and (min-width: 414px) {
  .popup .wrapper-popup2{
    min-width: 414px;
    height: 300px;
  }
	.popup .wrapper-popup3 {
		 min-width: 414px;
    	height:530px;
	}
}
@media only screen and (min-width:450px) {
  .popup .wrapper-popup2{
    min-width: 440px;
    height: 300px;
  }
	.popup .wrapper-popup3 {
		min-width: 450px;
    	height:550px;
	}
}
@media only screen and (min-width:480px) {
  .popup .wrapper-popup2{
    min-width: 470px;
    height: 320px;
  }
	.popup .wrapper-popup3 {
		min-width: 480px;
    	height: 600px;
	}
}
@media only screen and (min-width: 540px) {
  .popup .wrapper-popup2{
    min-width: 520px;
    height: 350px;
  }
  .popup .wrapper-popup3 {
	min-width: 540px;
    height: 600px;
  }
}
@media only screen and (min-width: 600px) {
  .popup .wrapper-popup2{
    min-width: 580px;
    height: 400px;
  }
  .popup .wrapper-popup3 {
	min-width: 600px;
    height: 700px;
  }
}
@media only screen and (min-width: 768px) {
	.popup .wrapper-popup2{
    min-width: 758px;
    height: 500px;
  }
  .popup .wrapper-popup3 {
	min-width: 768px;
    height:800px;
  }
}
@media only screen and (min-width:912px) {
  .popup .wrapper-popup2{
      min-width: 900px;
	  height:600px;
  }
  .popup .wrapper-popup3 {
      min-width:912px;
	  height:1000px;
  }
}
@media only screen and (min-width: 1000px) {
  .popup .wrapper-popup2 {
      min-width:1142px;
	  height:657px;
	  background: url(../images/pop.png) no-repeat center center;
  }
  .popup .wrapper-popup3 {
      min-width:970px;
	  height:790px;
	  background: url(../images/pop2.png) no-repeat center center;
  }
}
.popup .content-new-detail1 {
	height:330px;
	margin:10px 10px 60px 10px;
}
.popup .content-new-detail2 {
	height:330px;
	margin:10px 10px 60px 10px;
}
@media only screen and (min-width: 360px) {
	.popup .content-new-detail1 {
	    height:420px;
		margin:10px 10px 60px 10px;
  	}
	.popup .content-new-detail2 {
	    height:370px;
		margin:20px 15px 60px 10px;
  	}
}
@media only screen and (min-width: 375px) {
	.popup .content-new-detail1 {
	    height:400px;
		margin:10px 10px 60px 10px;
  	}
}
@media only screen and (min-width: 414px) {
	.popup .content-new-detail1 {
	    height:520px;
		margin:10px 10px 60px 10px;
  	}
	.popup .content-new-detail2 {
	    height:430px;
		margin:20px 15px 60px 10px;
  	}
}
@media only screen and (min-width: 430px) {
	.popup .content-new-detail1 {
	    height:520px;
		margin:10px 15px 60px 10px;
  	}
	.popup .content-new-detail2 {
	    height:400px;
		margin:20px 15px 60px 10px;
  	}
}
@media only screen and (min-width: 450px) {
	.popup .content-new-detail2 {
	    height:470px;
		margin:20px 15px 60px 10px;
  	}
	.popup .content-new-detail2 {
	    height:420px;
		margin:30px 15px 60px 10px;
  	}
}
@media only screen and (min-width: 480px) {
	.popup .content-new-detail1 {
	    height:580px;
		margin:10px 15px 60px 10px;
  	}
	.popup .content-new-detail2 {
	    height:460px;
		margin:30px 15px 60px 10px;
  	}
}
@media only screen and (min-width: 540px) {
	.popup .content-new-detail1 {
	    height:480px;
		margin:10px 15px 60px 10px;
  	}
	.popup .content-new-detail2 {
	    height:450px;
		margin:20px 15px 60px 10px;
  	}
}
@media only screen and (min-width: 600px) {
	.popup .content-new-detail1 {
	    height:660px;
		margin:10px 20px 0 20px;
  	}
	.popup .content-new-detail2 {
	    height:530px;
		margin:40px 20px 0 20px;
  	}
}
@media only screen and (min-width:768px) {
	.popup .content-new-detail2 {
		height:620px;
		margin:50px 30px 0 40px;
	}
}
@media only screen and (min-width: 912px) {
	.popup .content-new-detail1 {
	    height:930px;
     	margin: 20px 40px 0 40px;
  	}
	.popup .content-new-detail2 {
		height:800px;
		margin:50px 30px 0 40px;
	}
}
@media only screen and (min-width: 992px) {
	.popup .content-new-detail1 {
	    height: 630px;
     	margin: 20px 30px 0 360px;
  	}
	.popup .content-new-detail2 {
		height:550px;
		margin:50px 30px 0 60px;
	}
}
.popup .title-detail{
  	width: 100%;
	background: none;
	margin: 0 auto;
	padding: 16px 0 5px 0;
}
.popup .title-detail2{
	background: none;
	margin: 0 auto;
	width:95%;
	padding:30px 0 10px 100px;
}
.popup .title-detail3{
	background: none;
	margin: 0 auto;
	width:75%;
	padding:30px 0 10px 100px;
}
.popup .title-detail img{
  	width:90%;
}
@media only screen and (min-width: 450px) {
	.popup .title-detail{
		padding: 20px 0 10px 0;
	}
	.popup .title-detail2{
		width:90%;
	    padding:30px 0 10px 120px;
  }
	.popup .title-detail3{
		width:70%;
	    padding:30px 0 10px 110px;
  }
}
@media only screen and (min-width: 540px) {
	.popup .title-detail{
		padding: 25px 0 10px 0;
	}
	.popup .title-detail2{
		width:90%;
	    padding:40px 0 10px 120px;
  }
	.popup .title-detail3{
		width:70%;
	    padding:40px 0 10px 110px;
  }
}
@media only screen and (min-width: 768px) {
	.popup .title-detail2{
		width:80%;
	    padding:60px 0 10px 250px;
  }
	.popup .title-detail3{
		width:70%;
	    padding:60px 0 10px 210px;
  }
}
@media only screen and (min-width:800px) {
	.popup .title-detail2{
		width:90%;
	    padding:60px 0 10px 250px;
  }
	.popup .title-detail3{
		width:70%;
	    padding:60px 0 10px 210px;
  }
}
@media only screen and (min-width: 1000px) {
  .popup .title-detail{
	  padding:50px 0 10px 0;
  }
  .popup .title-detail img{
		width:80%;
	}
  .popup .title-detail2{
		width:75%;
		padding:72px 0 0 300px;
	}
	 .popup .title-detail3{
		width:65%;
		padding:62px 0 0 300px;
	}
}
.thele-text{
	margin-top: 0;
	line-height: 18px;
	font-weight: 500;
	font-size: 12px;
	color: #000000;
}
.thele-text p{
	padding: 5px 0 5px 0;
}
.thele-text span{
	font-size: 12px;
	color: #FF0000;
}
.thele-text a{
	color: #FF0000;
	text-decoration: none;
}
.thele-text a:hover{
	color: #0086D0;
	text-decoration: none;
}
@media only screen and (min-width: 375px) {
	.thele-text{
		line-height: 22px;
		font-size: 14px;
	}
	.thele-text span{
		font-size: 14px;
	}
}
@media only screen and (min-width:480px) {
	.thele-text{
		line-height: 25px;
		font-size: 16px;
	}
	.thele-text span{
		font-size: 16px;
	}
}
@media only screen and (min-width:600px) {
	.thele-text{
		line-height: 25px;
		font-size: 16px;
	}
	.thele-text span{
		font-size: 16px;
	}
}
@media only screen and (min-width: 1024px) {
	.thele-text .text{
    	font-size: 20px;
    }
	.thele-text .text p{
		padding: 5px 0 5px 0;
	}
	.thele-text.text span{
		font-size: 18px;
		font-weight: bold;
	}	
}

/*---- close----*/
.popup .close-content {
  	position: absolute;
  	background: url(../images/close.png) no-repeat center top;
  	background-size: contain;
  	width:31px;
    height:31px;
    bottom:-28px;
    right:45%;
  	cursor: pointer;
  	z-index: 999;
}
.popup .close-content2 {
  	position: absolute;
  	background: url(../images/close.png) no-repeat center top;
  	background-size: contain;
  	width:31px;
    height:31px;
    bottom:-28px;
    right:45%;
  	cursor: pointer;
  	z-index: 999;
}
@media only screen and (min-width: 430px) {
  .popup .close-content {
  	width:35px;
    height:35px;
    bottom:-30px;
    right:45%;
  }
	.popup .close-content2 {
  	width:35px;
    height:35px;
    bottom:-30px;
    right:45%;
  }
}
@media only screen and (min-width: 540px) {
  .popup .close-content {
  	width:40px;
    height:40px;
    bottom:-35px;
    right:45%;
  }
	.popup .close-content2 {
  	width:40px;
    height:40px;
    bottom:-35px;
    right:45%;
  }
}
@media only screen and (min-width: 600px) {
  .popup .close-content {
  	width:45px;
    height:45px;
    bottom:-45px;
    right:45%;
  }
	.popup .close-content2 {
  	width:45px;
    height:45px;
    bottom:-45px;
    right:45%;
  }
}
@media only screen and (min-width: 768px) {
  .popup .close-content {
  	width:51px;
    height:51px;
    bottom:-65px;
    right:45%;
  }
	.popup .close-content2 {
  	width:51px;
    height:51px;
    bottom:-65px;
    right:45%;
  }
}
@media only screen and (min-width: 992px) {
  .popup .close-content {
  	width:51px;
    height:51px;
    top:50px;
    right:30px;
  }
  .popup .close-content2 {
  	width:51px;
    height:51px;
    top:40px;
    right:-40px;
  }
}

/*------------dang ky thong tin-----------*/
.regis-box{
	display: flex;
	flex-wrap: wrap;
	text-align: center;
	justify-content: center;
	width:78%;
	margin:0 11% 0 11%;
}
.login-box{
	display: flex;
	flex-wrap: wrap;
	text-align: center;
	justify-content: center;
	width: 63%;
	margin:0 2% 0 35%;
}
.thongbao-box{
	display: flex;
	flex-wrap: wrap;
	text-align: center;
	justify-content: center;
	width:96%;
	margin:3% 2% 0 2%;
}
.regis-box .input,.login-box .input{
	width: 100%;
	height: 30px;
	background-color:#272523;
	border: 1px solid #ffffff;
	border-radius: 6px;
	margin:10px 0 3px 0;
}
.regis-box .input input,.login-box .input input{
	background:none;
	width: 100%;
	height: 30px;
	border: none;
	padding:0 10px 0 10px;
	text-align: center;
	color: #efefe1;
	font-size:12px;
	outline: none;
}
.regis-box .input input::placeholder, .login-box .input input::placeholder{
    color: #efefe1;text-align: center;font-weight: bold;
}
.regis-box a, .login-box a{
	color: #ffd73e;
}
.bt-nhan {
	display:flex;
	justify-content: center;
	width:148px;
	height:37px;
	background: url("../images/bt-pop.png") no-repeat left top;
	background-size: contain;
	margin:2% 0 0 25%;
	outline: none;
	vertical-align: middle;
	align-items: center;
	text-align: center;
	color: #ffffff;
	font-size: 14px;
}
.bt-dk {
	display:flex;
	justify-content: center;
	width:148px;
	height:37px;
	background: url("../images/bt-regis.png") no-repeat left top;
	background-size: contain;
	margin:4% 0 0 40%;
	outline: none;
	vertical-align: middle;
	align-items: center;
	text-align: center;
	color: #ffffff;
	font-size: 14px;
}
.bt-nhan:hover,.bt-dk:hover{
	filter:saturate(2);
	text-decoration: none;
	outline: none;
	color: #ffffff;
}
.thongbao-box{
		width: 65%;
		margin:1% 3% 0 35%;
	}
.thongbao-box p{
	color: #000000;
	font-size: 12px;
	line-height:18px;
}
.thongbao-box span{
	color: #cb0000;
	font-size: 18px;
	line-height:18px;
}
.gift{
	display: flex;
	flex-direction: column;
	text-align: center;
	justify-content: center;
	vertical-align: middle;
	width: 65%;
	margin:3% 3% 0 35%;
}
.gift p{
	color: #000000;
	font-size: 12px;
	line-height: 20px;
}
.gift t{
	width:75%;
	background: #34322f;
	border: 1px solid #ffffff;
	padding:8px;
	text-align: center;
	color: #ffed8e;
	margin:2% 0 0 12%;
}

@media only screen and (min-width:360px) {
	.regis-box{
		width: 70%;
		margin:0% 3% 0 16%;
	}
	.login-box{
		width: 55%;
		margin:1% 5% 0 40%;
	}
	.regis-box .input,.login-box .input{
		height: 37px;
		margin:3px 0 3px 0;
	}
	.regis-box .input input,.login-box .input input{
		height: 37px;
		padding:0 10px 0 10px;
	}
	.bt-nhan{
		width: 170px;
		height:45px;
		font-size: 16px;
		margin:3% 0 0 25%;
	}
	.bt-dk{
		width: 160px;
		height:45px;
		font-size: 16px;
		margin:5% 0 0 43%;
	}
	.thongbao-box{
		width: 65%;
		margin:1% 3% 0 35%;
	}
	.thongbao-box p{
		font-size: 14px;
		line-height: 18px;
	}
	.thongbao-box span{
		font-size:20px;
		line-height: 32px;
	}
	.gift{
		width: 65%;
		margin:1% 3% 0 35%;
	}
	.gift p{
		font-size:14px;
		line-height: 20px;
	}
	.gift t{
		width:75%;
		padding:10px;
		margin:2% 0 0 12%;
	}
}
@media only screen and (min-width:414px) {
	.regis-box{
		width: 70%;
		margin:1% 3% 0 16%;
	}
	.login-box{
		width: 55%;
		margin:3% 5% 0 40%;
	}
	.regis-box .input,.login-box .input{
		height: 38px;
		margin:3px 0 5px 0;
	}
	.regis-box .input input,.login-box .input input{
		height: 38px;
		padding:0 10px 0 10px;
	}
	.bt-nhan{
		width: 170px;
		height:45px;
		font-size: 16px;
		margin:3% 0 0 28%;
	}
	.bt-dk{
		width: 170px;
		height:45px;
		font-size: 16px;
		margin:3% 0 0 44%;
	}
	.thongbao-box{
		width: 65%;
		margin:3% 3% 0 35%;
	}
	.thongbao-box p{
		font-size: 16px;
		line-height: 21px;
	}
	.thongbao-box span{
		font-size:20px;
		line-height: 32px;
	}
	.gift{
		width: 65%;
		margin:3% 3% 0 35%;
	}
	.gift p{
		font-size:16px;
		line-height: 22px;
	}
	.gift t{
		width:75%;
		padding:13px;
		margin:2% 0 0 12%;
		font-size:16px;
	}
}
@media only screen and (min-width:450px) {
	.regis-box .input,.login-box .input{
		height: 40px;
	}
	.regis-box .input input,.login-box .input input{
		height: 40px;
	}
	.bt-nhan{
		width: 160px;
		height: 42px;
		font-size: 14px;
	}
	.bt-dk{
		width: 180px;
		height: 42px;
		font-size: 14px;
		margin:3% 0 0 45%;
	}
}
@media only screen and (min-width:480px) {
	.regis-box{
		width: 70%;
		margin:2% 3% 0 16%;
	}
	.login-box{
		width: 55%;
		margin:5% 5% 0 40%;
	}
	.regis-box .input,.login-box .input{
		height: 40px;
		margin:3px 0 5px 0;
	}
	.regis-box .input input,.login-box .input input{
		height: 40px;
		font-size:12px;
	}
	.bt-nhan{
		width: 188px;
		height:52px;
		font-size:18px;
		margin:2% 0 0 27%;
	}
	.bt-dk{
		width: 188px;
		height:52px;
		font-size:18px;
		margin:3% 0 0 46%;
	}
	.thongbao-box{
		width: 60%;
		margin:5% 5% 0 35%;
	}
	.thongbao-box p{
		font-size: 16px;
		line-height: 25px;
	}
	.thongbao-box span{
		font-size:20px;
		line-height: 32px;
	}
	.gift{
		width: 60%;
		margin:5% 5% 5% 35%;
	}
	.gift p{
		font-size:17px;
		line-height: 22px;
	}
	.gift t{
		width:75%;
		padding:10px;
		margin:2% 0 0 12%;
		font-size:17px;
		line-height: 22px;
	}
}
@media only screen and (min-width:540px) {
	.regis-box{
		width: 70%;
		margin:2% 3% 0 16%;
	}
	.regis-box .input,.login-box .input{
		height: 47px;
		margin:3px 0 10px 0;
	}
	.regis-box .input input,.login-box .input input{
		height: 47px;
		font-size:14px;
	}
	.bt-nhan{
		width: 198px;
		height: 54px;
		font-size:20px;
		margin:2% 0 0 30%;
	}
	.bt-dk{
		width: 198px;
		height: 54px;
		font-size:20px;
		margin:3% 0 0 47%;
	}
	.gift{
		width: 62%;
		margin:5% 3% 5% 35%;
	}
}
@media only screen and (min-width:600px) {
	.regis-box{
		width: 55%;
		margin:5% 5% 0 40%;
	}
	.regis-box .input,.login-box .input{
		height: 51px;
	}
	.regis-box .input input,.login-box .input input{
		height: 51px;
		font-size:16px;
	}
	.bt-nhan{
		width: 208px;
		height: 60px;
		font-size: 20px;
		margin:3% 0 0 29%;
	}
	.bt-dk{
		width: 208px;
		height: 60px;
		font-size: 20px;
		margin:3% 0 0 49%;
	}
	.thongbao-box p{
		font-size: 20px;
		line-height: 32px;
	}
	.thongbao-box span{
		font-size:25px;
		line-height: 32px;
	}
	.gift p{
		font-size: 20px;
		line-height: 32px;
	}
	.gift t{
		width:75%;
		padding:13px;
		margin: 2% 0 0 12%;
	}
}
@media only screen and (min-width:768px) {
	.regis-box .input,.login-box .input{
		height:60px;
		margin:10px 0 10px 0;
	}
	.regis-box .input input,.login-box .input input{
		height:60px;
		font-size:22px;
	}
	.bt-nhan{
		width: 284px;
		height:71px;
		font-size:25px;
		margin:3% 0 0 33%;
	}
	.bt-dk{
		width:300px;
		height:62px;
		font-size:25px;
		margin:4% 0 0 47%;
	}
	.thongbao-box p{
		font-size: 20px;
		line-height: 32px;
	}
	.thongbao-box span{
		font-size: 30px;
		line-height: 32px;
	}
}
@media only screen and (min-width:912px) {
	.regis-box .input,.login-box .input{
		height:75px;
		margin:10px 0 10px 0;
	}
	.regis-box .input input,.login-box .input input{
		height:75px;
		font-size:22px;
	}
	.bt-nhan{
		width: 284px;
		height:71px;
		font-size:25px;
		margin:3% 0 0 33%;
	}
	.bt-dk{
		width:340px;
		height:72px;
		font-size:25px;
		margin:4% 0 0 47%;
	}
	.thongbao-box p{
		font-size: 20px;
		line-height: 32px;
	}
	.thongbao-box span{
		font-size: 30px;
		line-height: 32px;
	}
}
@media only screen and (min-width: 992px) {
	.regis-box{
		width: 55%;
		margin:15% 10% 0 26%;
	}
	.login-box{
		width: 64%;
		margin:8% 18% 0 18%;
	}
	.regis-box .input,.login-box .input{
		height:65px;
		margin:25px 0 3px 0;
	}
	.regis-box .input input,.login-box .input input{
		height: 65px;
		font-size:18px;
	}
	.bt-nhan{
		width: 284px;
		height:71px;
		font-size:25px;
		margin:3% 0 0 38%;
	}
	.bt-dk{
		width:340px;
		height:72px;
		font-size:25px;
		margin:3% 0 0 27%;
	}
	.gift{
		width: 76%;
		margin:8% 12% 5% 12%;
	}
	.gift p{
		color: #000000;
		font-size: 25px;
		line-height: 32px;
	}
	.gift img{
		width:30%;
		padding: 10px;
	}
	.gift t{
		width:75%;
		padding:20px;
		margin:3% 0 0 12%;
	}
	.thongbao-box{
		width:70%;
		margin:10% 15% 0 15%;
	}
}
