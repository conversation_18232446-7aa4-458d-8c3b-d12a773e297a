

//slide class
var classNow = 0;
var class_swiper;

// Khởi tạo class swiper một cách an toàn
if (typeof Swiper !== 'undefined' && $('.class_swiper').length > 0) {
    try {
        class_swiper = new Swiper('.class_swiper',{
            effect : 'fade',
            fadeEffect: {
                crossFade: true
            },
            loop:true,
            on: {
                init: function(swiper) {
                    if (typeof swiperAnimateCache === 'function') {
                        swiperAnimateCache(swiper);
                    }
                    if (typeof swiperAnimate === 'function') {
                        swiperAnimate(swiper);
                    }
                },
                transitionEnd: function(swiper){
                    if (typeof swiperAnimate === 'function') {
                        swiperAnimate(swiper);
                    }
                    classNow = swiper.realIndex;
                    $('.class_tab li').removeClass('on');
                    $('.class_tab li').eq(classNow).addClass('on');
                }
            }
        });
    } catch (e) {
        console.warn('Error initializing class_swiper:', e);
    }
}
// <PERSON>ử lý click cho class tab
if ($('.class_tab li').length > 0) {
    $('.class_tab li').each(function(i){
        $(this).click(function(){
            $('.class_tab li').removeClass('on');
            $('.class_tab li').eq(i).addClass('on');
            if (class_swiper && typeof class_swiper.slideTo === 'function') {
                class_swiper.slideTo(i+1);
            }
        });
    });
}

jQuery(document).ready(function(){


    // slick mon phai
    var $slideMonPhai = $('.slide-mon-phai');
    if ($slideMonPhai.length > 0 && typeof $slideMonPhai.slick === 'function') {
        try {
            $slideMonPhai.slick({
                dots: true,
                arrows: false,
                // autoplay: true,
            });
        } catch (e) {
            console.warn('Error initializing slick slider:', e);
        }
    }


});




















