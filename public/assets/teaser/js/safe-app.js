// Safe replacement for app.js functionality
$(document).ready(function() {
    
    // Safe jQuery extension for handling missing elements
    $.fn.safeAttr = function(name, value) {
        if (this.length === 0) {
            return undefined;
        }
        try {
            if (value !== undefined) {
                return this.attr(name, value);
            } else {
                return this.attr(name);
            }
        } catch (e) {
            console.warn('Error in safeAttr:', e);
            return undefined;
        }
    };
    
    // Safe element access function
    function safeElement(selector) {
        try {
            var $el = $(selector);
            return $el.length > 0 ? $el : null;
        } catch (e) {
            console.warn('Error accessing element:', selector, e);
            return null;
        }
    }
    
    // Safe getAttribute function
    function safeGetAttribute(element, attributeName) {
        try {
            if (element && element.getAttribute && typeof element.getAttribute === 'function') {
                return element.getAttribute(attributeName);
            }
            return null;
        } catch (e) {
            console.warn('Error in safeGetAttribute:', e);
            return null;
        }
    }
    
    // Initialize lazy loading if needed
    function initLazyLoading() {
        var $lazyImages = $('.lazyload');
        if ($lazyImages.length > 0) {
            $lazyImages.each(function() {
                var $img = $(this);
                var dataSrc = $img.attr('data-src');
                if (dataSrc) {
                    $img.attr('src', dataSrc);
                    $img.removeClass('lazyload');
                }
            });
        }
    }
    
    // Initialize swiper if available
    function initSwiper() {
        if (typeof Swiper !== 'undefined') {
            // Features carousel
            var $featuresCarousel = $('.features__carousel .swiper-container');
            if ($featuresCarousel.length > 0) {
                try {
                    new Swiper($featuresCarousel[0], {
                        slidesPerView: 1,
                        spaceBetween: 30,
                        loop: true,
                        pagination: {
                            el: '.swiper-pagination',
                            clickable: true,
                        },
                        navigation: {
                            nextEl: '.swiper-button-next',
                            prevEl: '.swiper-button-prev',
                        },
                    });
                } catch (e) {
                    console.warn('Error initializing features swiper:', e);
                }
            }
        }
    }
    
    // Initialize popup functionality
    function initPopups() {
        // Show popup
        $('.show-dangky').on('click', function(e) {
            e.preventDefault();
            var $popup = $('#popup-dangky');
            if ($popup.length > 0) {
                $popup.show();
            }
        });
        
        $('.show-thele').on('click', function(e) {
            e.preventDefault();
            var $popup = $('#popup-thele');
            if ($popup.length > 0) {
                $popup.show();
            }
        });
        
        $('.show-thongbao').on('click', function(e) {
            e.preventDefault();
            var $popup = $('#popup-thongbao');
            if ($popup.length > 0) {
                $popup.show();
            }
        });
        
        // Close popup
        $('.close-content, .close-content2').on('click', function(e) {
            e.preventDefault();
            $(this).closest('.popup').hide();
        });
        
        // Close popup when clicking outside
        $('.popup').on('click', function(e) {
            if (e.target === this) {
                $(this).hide();
            }
        });
    }
    
    // Initialize fancybox if available
    function initFancybox() {
        if (typeof $.fancybox !== 'undefined') {
            try {
                $('[data-fancybox]').fancybox({
                    youtube: {
                        controls: 1,
                        showinfo: 0
                    }
                });
            } catch (e) {
                console.warn('Error initializing fancybox:', e);
            }
        }
    }
    
    // Main initialization
    function init() {
        initLazyLoading();
        initSwiper();
        initPopups();
        initFancybox();
        
        console.log('Safe app initialized successfully');
    }
    
    // Run initialization
    init();
    
    // Re-run lazy loading after a delay to catch any missed images
    setTimeout(initLazyLoading, 1000);
});
