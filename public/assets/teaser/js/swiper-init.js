// Swiper Initialization - Safe version
$(document).ready(function() {
    
    // Initialize Class Swiper (Section 5)
    function initClassSwiper() {
        const classSwiperContainer = $('.class_swiper');
        if (classSwiperContainer.length > 0 && typeof Swiper !== 'undefined') {
            try {
                const classSwiper = new Swiper('.class_swiper', {
                    effect: 'fade',
                    fadeEffect: {
                        crossFade: true
                    },
                    loop: true,
                    autoplay: {
                        delay: 4000,
                        disableOnInteraction: false,
                    },
                    on: {
                        init: function(swiper) {
                            if (typeof swiperAnimateCache === 'function') {
                                swiperAnimateCache(swiper);
                            }
                            if (typeof swiperAnimate === 'function') {
                                swiperAnimate(swiper);
                            }
                        },
                        transitionEnd: function(swiper) {
                            if (typeof swiperAnimate === 'function') {
                                swiperAnimate(swiper);
                            }
                            const classNow = swiper.realIndex;
                            $('.class_tab li').removeClass('on');
                            $('.class_tab li').eq(classNow).addClass('on');
                        }
                    }
                });

                // Handle tab clicks
                $('.class_tab li').each(function(i) {
                    $(this).click(function() {
                        $('.class_tab li').removeClass('on');
                        $('.class_tab li').eq(i).addClass('on');
                        if (classSwiper && typeof classSwiper.slideTo === 'function') {
                            classSwiper.slideTo(i + 1);
                        }
                    });
                });

                console.log('Class Swiper initialized successfully');
                return classSwiper;
            } catch (e) {
                console.warn('Error initializing class swiper:', e);
                return null;
            }
        } else {
            console.warn('Class swiper container not found or Swiper library not loaded');
            return null;
        }
    }

    // Initialize Features Swiper (Section 6)
    function initFeaturesSwiper() {
        const featuresSwiperContainer = $('.features__carousel .swiper-container');
        if (featuresSwiperContainer.length > 0 && typeof Swiper !== 'undefined') {
            try {
                const featuresSwiper = new Swiper(featuresSwiperContainer[0], {
                    slidesPerView: 1,
                    spaceBetween: 30,
                    loop: true,
                    autoplay: {
                        delay: 3000,
                        disableOnInteraction: false,
                    },
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                });

                console.log('Features Swiper initialized successfully');
                return featuresSwiper;
            } catch (e) {
                console.warn('Error initializing features swiper:', e);
                return null;
            }
        } else {
            console.warn('Features swiper container not found or Swiper library not loaded');
            return null;
        }
    }

    // Wait for all resources to load before initializing
    $(window).on('load', function() {
        // Small delay to ensure all scripts are loaded
        setTimeout(function() {
            initClassSwiper();
            initFeaturesSwiper();
        }, 100);
    });

    // Fallback: Initialize after DOM ready if window load doesn't fire
    setTimeout(function() {
        if ($('.class_swiper').length > 0 && !$('.class_swiper').hasClass('swiper-initialized')) {
            initClassSwiper();
        }
        if ($('.features__carousel .swiper-container').length > 0 && !$('.features__carousel .swiper-container').hasClass('swiper-initialized')) {
            initFeaturesSwiper();
        }
    }, 2000);
});
