/* Burger Menu Styles */

/* Burger button styling */
#navBurger {
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10000;
    position: relative;
}

#navBurger:hover {
    opacity: 0.8;
}

#navBurger.active {
    transform: rotate(90deg);
}

/* Mobile navigation styles */
@media (max-width: 768px) {
    /* Hide desktop navigation items on mobile */
    .floattop__nav .desktop {
        display: none !important;
    }
    
    /* Mobile navigation when active */
    .floattop__nav.mobile-active {
        display: flex !important;
        flex-direction: column !important;
        position: absolute !important;
        top: 100% !important;
        left: 0 !important;
        right: 0 !important;
        background: rgba(0, 0, 0, 0.95) !important;
        z-index: 9999 !important;
        padding: 20px !important;
        border-radius: 0 0 10px 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        animation: slideDown 0.3s ease;
    }
    
    .floattop__nav.mobile-active li {
        margin: 10px 0 !important;
        text-align: center;
    }
    
    .floattop__nav.mobile-active .floatnav__item {
        color: #fff !important;
        text-decoration: none;
        padding: 10px 15px;
        border-radius: 5px;
        transition: all 0.3s ease;
        display: block;
    }
    
    .floattop__nav.mobile-active .floatnav__item:hover,
    .floattop__nav.mobile-active .floatnav__item.active {
        background: rgba(255, 255, 255, 0.1);
        color: #ffd700 !important;
    }
    
    /* Search container in mobile menu */
    .floattop__nav.mobile-active .search-container {
        margin: 15px 0;
    }
    
    .floattop__nav.mobile-active .search-container input {
        width: 100%;
        padding: 10px;
        border: 1px solid #333;
        border-radius: 5px;
        background: rgba(255, 255, 255, 0.1);
        color: #fff;
    }
    
    .floattop__nav.mobile-active .search-container input::placeholder {
        color: #ccc;
    }
    
    /* Body scroll lock when menu is open */
    body.nav-open {
        overflow: hidden;
    }
    
    /* Floattop container adjustments */
    .floattop.menu-open {
        position: relative;
        z-index: 9998;
    }
}

/* Animation for mobile menu */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Desktop styles - ensure normal behavior */
@media (min-width: 769px) {
    .floattop__nav {
        display: flex !important;
        position: static !important;
        background: transparent !important;
        padding: 0 !important;
        flex-direction: row !important;
    }
    
    #navBurger {
        display: none !important;
    }
    
    .group__icon.mobile-flex {
        display: none !important;
    }
}

/* Show burger and mobile icons only on mobile */
@media (max-width: 768px) {
    #navBurger {
        display: block !important;
    }
    
    .group__icon.mobile-flex {
        display: flex !important;
    }
    
    /* Hide some desktop elements */
    .floattop__nav li.desktop {
        display: none !important;
    }
}
