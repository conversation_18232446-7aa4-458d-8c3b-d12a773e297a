// Burger Menu Handler
$(document).ready(function() {
    console.log('Burger menu script loaded');
    console.log('Found burger button:', $('#navBurger').length);
    console.log('Found navigation:', $('#floatnav').length);

    // Handle burger menu click
    $('#navBurger').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Toggle burger menu active state
        $(this).toggleClass('active');

        // Toggle navigation menu
        const floatNav = $('#floatnav, .floatnav, .floattop__nav');
        const floatTop = $('.floattop');
        const body = $('body');

        // Add/remove active classes
        floatNav.toggleClass('mobile-active');
        floatTop.toggleClass('menu-open');
        body.toggleClass('nav-open');

        // Show/hide navigation items on mobile
        if (floatNav.hasClass('mobile-active')) {
            floatNav.css({
                'display': 'flex',
                'flex-direction': 'column',
                'position': 'absolute',
                'top': '100%',
                'left': '0',
                'right': '0',
                'background': '#000',
                'z-index': '9999',
                'padding': '20px'
            });
        } else {
            floatNav.removeAttr('style');
        }

        console.log('Burger menu clicked - Navigation toggled');
    });

    // Close menu when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#navBurger, #floatnav, .floatnav, .floattop__nav').length) {
            $('#navBurger').removeClass('active');
            $('#floatnav, .floatnav, .floattop__nav').removeClass('mobile-active').removeAttr('style');
            $('.floattop').removeClass('menu-open');
            $('body').removeClass('nav-open');
        }
    });

    // Handle window resize
    $(window).on('resize', function() {
        if ($(window).width() > 768) {
            $('#navBurger').removeClass('active');
            $('#floatnav, .floatnav, .floattop__nav').removeClass('mobile-active').removeAttr('style');
            $('.floattop').removeClass('menu-open');
            $('body').removeClass('nav-open');
        }
    });

    console.log('Burger menu handler initialized');
});
