// Burger Menu Handler
$(document).ready(function() {
    
    // Handle burger menu click
    $('#navBurger').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // Toggle mobile menu
        const mobileMenu = $('.mobile-menu, .nav-mobile, .header-mobile-menu');
        const body = $('body');
        
        // Add/remove active classes
        $(this).toggleClass('active');
        mobileMenu.toggleClass('active open');
        body.toggleClass('menu-open');
        
        // If no specific mobile menu found, try to toggle navigation
        if (mobileMenu.length === 0) {
            $('.main-navigation, .navigation, .header-nav').toggleClass('mobile-active');
        }
        
        console.log('Burger menu clicked');
    });
    
    // Close menu when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#navBurger, .mobile-menu, .nav-mobile, .header-mobile-menu').length) {
            $('#navBurger').removeClass('active');
            $('.mobile-menu, .nav-mobile, .header-mobile-menu').removeClass('active open');
            $('body').removeClass('menu-open');
            $('.main-navigation, .navigation, .header-nav').removeClass('mobile-active');
        }
    });
    
    // Handle window resize
    $(window).on('resize', function() {
        if ($(window).width() > 768) {
            $('#navBurger').removeClass('active');
            $('.mobile-menu, .nav-mobile, .header-mobile-menu').removeClass('active open');
            $('body').removeClass('menu-open');
            $('.main-navigation, .navigation, .header-nav').removeClass('mobile-active');
        }
    });
    
    console.log('Burger menu handler initialized');
});
