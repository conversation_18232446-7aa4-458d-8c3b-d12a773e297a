{"name": "jquery-validation", "title": "jQuery Validation Plugin", "description": "Form validation made easy", "version": "1.11.1", "homepage": "https://github.com/jzaefferer/jquery-validation", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "joern.z<PERSON><PERSON><PERSON>@gmail.com", "url": "http://bassistance.de"}, "repository": {"type": "git", "url": "git://github.com/jzaefferer/jquery-validation.git"}, "bugs": {"url": "https://github.com/jzaefferer/jquery-validation/issues"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "scripts": {"test": "grunt jshint qunit"}, "dependencies": {}, "devDependencies": {"grunt": "0.4.x", "grunt-contrib-qunit": "~0.2.0", "grunt-contrib-jshint": "~0.2.0", "grunt-contrib-uglify": "~0.1.1", "grunt-contrib-concat": "~0.1.3", "grunt-zipstream": "~0.2.2"}, "keywords": ["forms", "validation", "validate"]}