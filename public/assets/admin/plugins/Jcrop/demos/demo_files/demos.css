/* Jcrop Demo Site CSS - 2012 Tapmodo Interactive LLC - MIT License
   Not required to run Jcrop - contains twitter bootstrap code */
/* To build these CSS files you must have LESS and run
 * $ git submodule init
 * $ git submodule update
 * ...to pull in the Twitter bootstrap files
 */
.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
  line-height: 0;
}
.clearfix:after {
  clear: both;
}
.hide-text {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.input-block-level {
  display: block;
  width: 100%;
  min-height: 30px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
/* JCROP DEMOS CSS */
li small {
  color: #f07878;
}
.inline-labels label {
  display: inline;
}
div#interface.span3 fieldset {
  margin-bottom: 1.5em;
}
div#interface.span3 fieldset legend {
  margin-bottom: 2px;
  padding-bottom: 2px;
  line-height: 1.2;
}
.article h1 {
  color: #333;
  margin-top: .2em;
}
.jc-demo {
  text-align: center;
}
.jcropper-holder {
  border: 1px #bbb solid;
}
.jc-demo-box {
  text-align: left;
  margin: 2em auto;
  background: white;
  border: 1px #bbb solid;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.25);
  box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.25);
  padding: 1em 2em 2em;
}
form {
  margin: 1.5em 0;
}
form.coords label {
  margin-right: 1em;
  font-weight: bold;
  color: #900;
}
form.coords input {
  width: 3em;
}
.ui-widget-overlay {
  opacity: 0.80;
  filter: alpha(opacity=70);
}
.jc-dialog {
  padding-top: 1em;
}
.ui-dialog p tt {
  color: yellow;
}
.jcrop-light .jcrop-selection {
  -moz-box-shadow: 0px 0px 15px #999;
  /* Firefox */

  -webkit-box-shadow: 0px 0px 15px #999;
  /* Safari, Chrome */

  box-shadow: 0px 0px 15px #999;
  /* CSS3 */

}
.jcrop-dark .jcrop-selection {
  -moz-box-shadow: 0px 0px 15px #000;
  /* Firefox */

  -webkit-box-shadow: 0px 0px 15px #000;
  /* Safari, Chrome */

  box-shadow: 0px 0px 15px #000;
  /* CSS3 */

}
.jcrop-fancy .jcrop-handle.ord-e {
  -webkit-border-top-left-radius: 0px;
  -webkit-border-bottom-left-radius: 0px;
}
.jcrop-fancy .jcrop-handle.ord-w {
  -webkit-border-top-right-radius: 0px;
  -webkit-border-bottom-right-radius: 0px;
}
.jcrop-fancy .jcrop-handle.ord-nw {
  -webkit-border-bottom-right-radius: 0px;
}
.jcrop-fancy .jcrop-handle.ord-ne {
  -webkit-border-bottom-left-radius: 0px;
}
.jcrop-fancy .jcrop-handle.ord-sw {
  -webkit-border-top-right-radius: 0px;
}
.jcrop-fancy .jcrop-handle.ord-se {
  -webkit-border-top-left-radius: 0px;
}
.jcrop-fancy .jcrop-handle.ord-s {
  -webkit-border-top-left-radius: 0px;
  -webkit-border-top-right-radius: 0px;
}
.jcrop-fancy .jcrop-handle.ord-n {
  -webkit-border-bottom-left-radius: 0px;
  -webkit-border-bottom-right-radius: 0px;
}
.description {
  margin: 16px 0;
}
.jcrop-droptarget canvas {
  background-color: #f0f0f0;
}
