/**
 * QUnit v1.5.0 - A JavaScript Unit Testing Framework
 *
 * http://docs.jquery.com/QUnit
 *
 * Copyright (c) 2012 <PERSON>, <PERSON><PERSON><PERSON>
 * Dual licensed under the MIT (MIT-LICENSE.txt)
 * or GPL (GPL-LICENSE.txt) licenses.
 */

/** Font Family and Sizes */

#qunit-tests, #qunit-header, #qunit-banner, #qunit-testrunner-toolbar, #qunit-userAgent, #qunit-testresult {
	font-family: "Helvetica Neue Light", "HelveticaNeue-Light", "Helvetica Neue", Calibri, Helvetica, Arial, sans-serif;
}

#qunit-testrunner-toolbar, #qunit-userAgent, #qunit-testresult, #qunit-tests li { font-size: small; }
#qunit-tests { font-size: smaller; }


/** Resets */

#qunit-tests, #qunit-tests ol, #qunit-header, #qunit-banner, #qunit-userAgent, #qunit-testresult {
	margin: 0;
	padding: 0;
}


/** Header */

#qunit-header {
	padding: 0.5em 0 0.5em 1em;

	color: #8699a4;
	background-color: #0d3349;

	font-size: 1.5em;
	line-height: 1em;
	font-weight: normal;

	border-radius: 15px 15px 0 0;
	-moz-border-radius: 15px 15px 0 0;
	-webkit-border-top-right-radius: 15px;
	-webkit-border-top-left-radius: 15px;
}

#qunit-header a {
	text-decoration: none;
	color: #c2ccd1;
}

#qunit-header a:hover,
#qunit-header a:focus {
	color: #fff;
}

#qunit-header label {
	display: inline-block;
}

#qunit-banner {
	height: 5px;
}

#qunit-testrunner-toolbar {
	padding: 0.5em 0 0.5em 2em;
	color: #5E740B;
	background-color: #eee;
}

#qunit-userAgent {
	padding: 0.5em 0 0.5em 2.5em;
	background-color: #2b81af;
	color: #fff;
	text-shadow: rgba(0, 0, 0, 0.5) 2px 2px 1px;
}


/** Tests: Pass/Fail */

#qunit-tests {
	list-style-position: inside;
}

#qunit-tests li {
	padding: 0.4em 0.5em 0.4em 2.5em;
	border-bottom: 1px solid #fff;
	list-style-position: inside;
}

#qunit-tests.hidepass li.pass, #qunit-tests.hidepass li.running  {
	display: none;
}

#qunit-tests li strong {
	cursor: pointer;
}

#qunit-tests li a {
	padding: 0.5em;
	color: #c2ccd1;
	text-decoration: none;
}
#qunit-tests li a:hover,
#qunit-tests li a:focus {
	color: #000;
}

#qunit-tests ol {
	margin-top: 0.5em;
	padding: 0.5em;

	background-color: #fff;

	border-radius: 15px;
	-moz-border-radius: 15px;
	-webkit-border-radius: 15px;

	box-shadow: inset 0px 2px 13px #999;
	-moz-box-shadow: inset 0px 2px 13px #999;
	-webkit-box-shadow: inset 0px 2px 13px #999;
}

#qunit-tests table {
	border-collapse: collapse;
	margin-top: .2em;
}

#qunit-tests th {
	text-align: right;
	vertical-align: top;
	padding: 0 .5em 0 0;
}

#qunit-tests td {
	vertical-align: top;
}

#qunit-tests pre {
	margin: 0;
	white-space: pre-wrap;
	word-wrap: break-word;
}

#qunit-tests del {
	background-color: #e0f2be;
	color: #374e0c;
	text-decoration: none;
}

#qunit-tests ins {
	background-color: #ffcaca;
	color: #500;
	text-decoration: none;
}

/*** Test Counts */

#qunit-tests b.counts                       { color: black; }
#qunit-tests b.passed                       { color: #5E740B; }
#qunit-tests b.failed                       { color: #710909; }

#qunit-tests li li {
	margin: 0.5em;
	padding: 0.4em 0.5em 0.4em 0.5em;
	background-color: #fff;
	border-bottom: none;
	list-style-position: inside;
}

/*** Passing Styles */

#qunit-tests li li.pass {
	color: #5E740B;
	background-color: #fff;
	border-left: 26px solid #C6E746;
}

#qunit-tests .pass                          { color: #528CE0; background-color: #D2E0E6; }
#qunit-tests .pass .test-name               { color: #366097; }

#qunit-tests .pass .test-actual,
#qunit-tests .pass .test-expected           { color: #999999; }

#qunit-banner.qunit-pass                    { background-color: #C6E746; }

/*** Failing Styles */

#qunit-tests li li.fail {
	color: #710909;
	background-color: #fff;
	border-left: 26px solid #EE5757;
	white-space: pre;
}

#qunit-tests > li:last-child {
	border-radius: 0 0 15px 15px;
	-moz-border-radius: 0 0 15px 15px;
	-webkit-border-bottom-right-radius: 15px;
	-webkit-border-bottom-left-radius: 15px;
}

#qunit-tests .fail                          { color: #000000; background-color: #EE5757; }
#qunit-tests .fail .test-name,
#qunit-tests .fail .module-name             { color: #000000; }

#qunit-tests .fail .test-actual             { color: #EE5757; }
#qunit-tests .fail .test-expected           { color: green;   }

#qunit-banner.qunit-fail                    { background-color: #EE5757; }


/** Result */

#qunit-testresult {
	padding: 0.5em 0.5em 0.5em 2.5em;

	color: #2b81af;
	background-color: #D2E0E6;

	border-bottom: 1px solid white;
}
#qunit-testresult .module-name {
	font-weight: bold;
}

/** Fixture */

#qunit-fixture {
	position: absolute;
	top: -10000px;
	left: -10000px;
	width: 1000px;
	height: 1000px;
}
