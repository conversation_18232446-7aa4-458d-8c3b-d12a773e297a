<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Dynatree - Example</title>

	<script src="../jquery/jquery.js" type="text/javascript"></script>
	<script src="../jquery/jquery-ui.custom.js" type="text/javascript"></script>
	<script src="../jquery/jquery.cookie.js" type="text/javascript"></script>
	<!-- Include the basic stylesheet: -->
	<link href="../src/skin-vista/ui.dynatree.css" rel="stylesheet" type="text/css">
	<!-- Override CSS with a custom stylesheet : -->
	<link href="skin-custom/custom.css" rel="stylesheet" type="text/css" >
	<script src="../src/jquery.dynatree.js" type="text/javascript"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="prettify.css" rel="stylesheet">
	<script src="prettify.js" type="text/javascript"></script>
	<link href="sample.css" rel="stylesheet" type="text/css">
	<script src="sample.js" type="text/javascript"></script>
	<!-- End_Exclude -->

<script type="text/javascript">
	$(function(){
		$("#tree").dynatree({
			title: "Sample Theming",
			// Image folder used for data.icon attribute.
			imagePath: "skin-custom/",
			onSelect: function(node) {
				alert ("You selected " + node);
			}
		});
	});
</script>
</head>

<body class="example">
	<h1>Example: Theming</h1>
	<p class="description">
		Includes a custom CSS <i>after</i> the standard CSS to overrride theming.<br>
		Some nodes have their <code>data.addClass</code> attribute set.<br>
		Finally, the last two nodes use the <code>data.icon</code> attribute.
	</p>

	<div id="tree">
		<ul>
			<li>Document with some children
				<ul>
					<li>Sub-item 4.1
					<ul>
						<li>Sub-item 4.1.1
						<li>Sub-item 4.1.2
					</ul>
					<li>Sub-item 4.2
				</ul>

			<li class="folder">A folder (different icon when expanded)
				<ul>
					<li>Node 2.1
					<li>Node 2.2
				</ul>

			<li data="addClass:'custom1'">Node with custom class 1

			<li data="isFolder: true, addClass:'custom2'">Folder with custom class 2
				<ul>
					<li data="addClass:'custom1'">Node 4.1 (using custom class 1)
					<li>Node 4.2
				</ul>

			<li data="icon: 'customDoc1.gif'">Node with standard CSS, but custom icon
			<li data="isFolder: true, icon: 'folder_docs.gif'">Folder with standard CSS but custom icon
				<ul>
					<li>Node 4.1
					<li>Node 4.2
				</ul>


		</ul>
	</div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="http://dynatree.googlecode.com">jquery.dynatree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="samples.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
