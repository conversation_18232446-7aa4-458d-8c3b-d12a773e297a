<?php

use App\Http\Controllers\IndexController;
use App\Http\Controllers\InviteController;
use App\Http\Controllers\PreLoginController;
use App\Http\Controllers\TintucController;
use App\Models\Category;
use Illuminate\Support\Facades\Route;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::any('/ckfinder/connector', '\CKSource\CKFinderBridge\Controller\CKFinderController@requestAction')
    ->name('ckfinder_connector');

Route::any('/ckfinder/browser', '\CKSource\CKFinderBridge\Controller\CKFinderController@browserAction')
    ->name('ckfinder_browser');

//Route::any('/ckfinder/examples/{example?}', '\CKSource\CKFinderBridge\Controller\CKFinderController@examplesAction')
//    ->name('ckfinder_examples');

//Route::get('/dang-ky', [IndexController::class, 'register'])->name('index.register');
//Route::post('/post-dang-ky', [IndexController::class, 'postRegister'])->name('index.post-register');
Route::group(['middleware' => ['check-ip']], function () {
    Route::get('/', function () {
        return redirect()->route('teaser');
    });
    Route::get('teaser', [\App\Http\Controllers\TeaserController::class, 'index'])->name('teaser');
    Route::get('wiki', [IndexController::class, 'index'])->name('index');
    Route::get('wiki/{detail}.html', [IndexController::class, 'general_detail'])->name('general_detail');

    Route::get('wiki/tin-tuc', [TintucController::class, 'tin_tuc'])->name('tin_tuc');
    Route::get('wiki/tin-tuc/tin-tuc-pagination', [TintucController::class, 'tin_tuc_pagination'])->name('tin_tuc_pagination');
    Route::get('wiki/tin-tuc/clip-pagination', [TintucController::class, 'clip_pagination'])->name('clip_pagination');
    Route::get('wiki/tin-tuc/{post}.html', [TintucController::class, 'tin_tuc_detail'])->name('tin_tuc_detail');

    Route::get('wiki/gioi-thieu', [\App\Http\Controllers\IndexController::class, 'gioi_thieu'])->name('gioi_thieu');

    Route::get('wiki/search', [IndexController::class, 'search'])->name('wiki.search');

    Route::get('index/tin-tuc', [\App\Http\Controllers\IndexController::class, 'indexTinTuc'])->middleware('api')->name('index-tin-tuc');
    Route::get('index/su-kien', [\App\Http\Controllers\IndexController::class, 'indexSuKien'])->middleware('api')->name('index-su-kien');
    Route::get('index/bao-tri', [\App\Http\Controllers\IndexController::class, 'indexBaoTri'])->middleware('api')->name('index-bao-tri');

//    Route::get('update-category', function () {
//        Category::truncate();
//        \App\Models\Category::insert
//        ([
//            [
//                'name' => 'Tin tức',
//                'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//                'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//            ],
//            [
//                'name' => 'Sụ kiện',
//                'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//                'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//            ],
//            [
//                'name' => 'Cập nhật',
//                'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//                'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//            ],
//            [
//                'name' => 'Bảo trì',
//                'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//                'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//            ],
//            [
//                'name' => 'Hướng dẫn tải game',
//                'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//                'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//            ],
//            [
//                'name' => 'Hướng dẫn cơ bản',
//                'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//                'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//            ],
//        ]);
//
//        return 0;
//    });

});

//Route::get('/download', [IndexController::class, 'download'])->name('dowhiepnload');

//Route::get('/backup', function () {
//    \App\Models\User::create
//    ([
//        'email' => '<EMAIL>',
//        'name' => 'Test Administrator',
//        'username' => 'vinhadmin',
//        'password' => \Illuminate\Support\Facades\Hash::make('12345678'),
//        'group' => GROUP_ADMIN,
//        'active' => ACTIVE_TRUE,
//        'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//        'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//    ]);
//    return 'ok';
//});

Route::group(['prefix' => 'auth', 'namespace' => 'App\\Http\\Controllers\\Admin'], function () {
    Route::get('login', function () {
        return view('auth.login');
    })->name('auth.getLogin');
    Route::post('login', 'LoginController@login')->name('auth.postLogin');
    Route::get('logout', 'LoginController@logout')->name('auth.postLogout');

    Route::get('forgot-password', function () {
        return view('auth.forgot-password');
    })->name('auth.getForgotPassword');
    Route::post('forgot-password', 'ForgotPasswordController@resetPassword')->name('auth.sendMail');

    Route::get('reset-password/{token}', 'ForgotPasswordController@getFormResetPassword')->name('auth.getRecoverPassword');
    Route::post('reset-password/{token}', 'ForgotPasswordController@resetPasswordChange')->name('auth.postRecoverPassword');
});
Route::group(['namespace' => 'App\\Http\\Controllers\\Admin', 'middleware' => ['auth','check-ip'], 'prefix' => '/admin'], function () {
    Route::get('/', 'AdminController@index')->name('admin.dashboard');
    Route::get('/log-pre-login', 'PreloginController@index')->name('admin.pre-login.list');

    Route::group(['namespace' => 'User', 'prefix' => 'users'], function () {
        Route::get('/', 'UserController@index')->name('admin.user.list');
        Route::get('/detail', 'UserController@detail')->name('admin.user.detail');
        Route::get('/form', 'UserController@getForm')->middleware('check-admin-permission')->name('admin.user.form.get');
        Route::post('/form', 'UserController@saveForm')->middleware('check-admin-permission')->name('admin.user.form.post');
        Route::get('/edit/{id}', 'UserController@editForm')->name('admin.user.form.edit');
        Route::post('/update/{id}', 'UserController@updateForm')->name('admin.user.form.update');
        Route::get('/delete/{id}', 'UserController@delete')->middleware('check-admin-permission')->name('admin.user.delete');
    });

    Route::group(['prefix' => 'categories'], function () {
        Route::get('/', 'CategoryController@index')->name('admin.category.list');
        Route::get('/form', 'CategoryController@getForm')->name('admin.category.form.get');
        Route::post('/form', 'CategoryController@saveForm')->name('admin.category.form.post');
        Route::get('/edit/{id}', 'CategoryController@editForm')->name('admin.category.form.edit');
        Route::post('/update/{id}', 'CategoryController@updateForm')->name('admin.category.form.update');
        Route::get('/delete/{id}', 'CategoryController@delete')->name('admin.category.delete');
    });

    Route::group(['prefix' => 'posts'], function () {
        Route::get('/', 'PostController@index')->name('admin.post.list');
        Route::get('/form', 'PostController@getForm')->name('admin.post.form.get');
        Route::post('/form', 'PostController@saveForm')->name('admin.post.form.post');
        Route::get('/edit/{id}', 'PostController@editForm')->name('admin.post.form.edit');
        Route::post('/update/{id}', 'PostController@updateForm')->name('admin.post.form.update');
        Route::get('/delete/{id}', 'PostController@delete')->name('admin.post.delete');
    });

    Route::group(['prefix' => 'clips'], function () {
        Route::get('/', 'ClipController@index')->name('admin.clip.list');
        Route::get('/form', 'ClipController@getForm')->name('admin.clip.form.get');
        Route::post('/form', 'ClipController@saveForm')->name('admin.clip.form.post');
        Route::get('/edit/{id}', 'ClipController@editForm')->name('admin.clip.form.edit');
        Route::post('/update/{id}', 'ClipController@updateForm')->name('admin.clip.form.update');
        Route::get('/delete/{id}', 'ClipController@delete')->name('admin.clip.delete');
    });

    Route::group(['prefix' => 'keywords'], function () {
        Route::get('/', 'KeywordController@index')->name('admin.keywords.list');
        Route::get('/form', 'KeywordController@getForm')->name('admin.keywords.form.get');
        Route::post('/form', 'KeywordController@saveForm')->name('admin.keywords.form.post');
        Route::get('/edit/{id}', 'KeywordController@editForm')->name('admin.keywords.form.edit');
        Route::post('/update/{id}', 'KeywordController@updateForm')->name('admin.keywords.form.update');
        Route::get('/delete/{id}', 'KeywordController@delete')->name('admin.keywords.delete');
    });

    Route::group(['prefix' => 'general'], function () {
        Route::get('/', 'GeneralController@index')->name('admin.general.list');
        Route::get('/form', 'GeneralController@getForm')->name('admin.general.form.get');
        Route::post('/form', 'GeneralController@saveForm')->name('admin.general.form.post');
        Route::get('/edit/{id}', 'GeneralController@editForm')->name('admin.general.form.edit');
        Route::post('/update/{id}', 'GeneralController@updateForm')->name('admin.general.form.update');
        Route::get('/delete/{id}', 'GeneralController@delete')->name('admin.general.delete');
    });
});

Route::get('/sitemap.xml', function () {
    $sitemap = Sitemap::create();
    // Thêm trang tĩnh
    $sitemap->add(Url::create('/')->setPriority(1.0)->setChangeFrequency('daily'));
    $sitemap->add(Url::create('/huong-dan')->setPriority(1.0)->setChangeFrequency('daily'));
    $sitemap->add(Url::create('/cam-nang')->setPriority(1.0)->setChangeFrequency('daily'));
    $sitemap->add(Url::create('/tin-tuc')->setPriority(1.0)->setChangeFrequency('daily'));
    $sitemap->add(Url::create('/landing')->setPriority(1.0)->setChangeFrequency('daily'));

    // Thêm bài viết từ database
    $news = \App\Models\Post::where('category_id', TINTUC)->get(); // Lấy tất cả bài viết từ database
    foreach ($news as $post) {
        $sitemap->add(
            Url::create(\route('tin_tuc_detail', $post->title_domain))
                ->setLastModificationDate($post->updated_at)
                ->setChangeFrequency('weekly')
                ->setPriority(0.9)
        );
    }

    $events = \App\Models\Post::where('category_id', SUKIEN)->get(); // Lấy tất cả bài viết từ database
    foreach ($events as $post) {
        $sitemap->add(
            Url::create(\route('su_kien_detail', $post->title_domain))
                ->setLastModificationDate($post->updated_at)
                ->setChangeFrequency('weekly')
                ->setPriority(0.9)
        );
    }

    $camnang = \App\Models\Post::with('category')->whereHas('category', function ($q) {
        $q->where('parent_id', CAMNANG);
    })->get(); // Lấy tất cả bài viết từ database
    foreach ($camnang as $post) {
        $sitemap->add(
            Url::create(\route('cam_nang_detail', $post->title_domain))
                ->setLastModificationDate($post->updated_at)
                ->setChangeFrequency('weekly')
                ->setPriority(0.9)
        );
    }

    $huongdan = \App\Models\Post::with('category')->whereHas('category', function ($q) {
        $q->where('parent_id', HUONGDAN);
    })->get(); // Lấy tất cả bài viết từ database
    foreach ($huongdan as $post) {
        $sitemap->add(
            Url::create(\route('huong_dan_detail', $post->title_domain))
                ->setLastModificationDate($post->updated_at)
                ->setChangeFrequency('weekly')
                ->setPriority(0.9)
        );
    }
    return $sitemap->toResponse(request());
});
