<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CheckIp
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
//        // <PERSON>h sách các IP được phép
//        $allowedIps = ['*************', '************', '*************', '127.0.0.1', '*************','*************']; // <PERSON>hay đ<PERSON>i IP theo yêu cầu
//        $ip = $request->header('x-forwarded-for', $request->header('cf-connecting-ip', $request->header('true-client-ip', $request->ip())));
//        $ip = explode(', ', $ip);
//
//        $matchedIps = array_intersect($ip, $allowedIps);
//
//        if (empty($matchedIps)) {
//            abort(403, 'Unauthorized action.');
//        }

        return $next($request);
    }
}

