<?php

namespace App\Http\Controllers;

use App\Models\Banner;
use App\Models\Category;
use App\Models\Clip;
use App\Models\Post;
use App\Models\Ranking;
use App\Models\Server;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class TintucController extends Controller
{
    public function tin_tuc() {
        $news = Post::where('category_id', TINTUC)->where('active', ACTIVE)->orderByDesc('created_at')->paginate(5);
        $clips = Clip::where('active', ACTIVE)->orderByDesc('created_at')->paginate(5);
        $keywords = $this->keywords();
        return view('tin-tuc', [
            'news' => $news,
            'clips' => $clips,
            'keywords' => $keywords
        ]);
    }

    public function su_kien() {
        $news = Post::where('category_id', SUKIEN)->where('active', ACTIVE)->orderByDesc('created_at')->paginate(8);
        $keywords = $this->keywords();
        return view('tin-tuc.su-kien', [
            'posts' => $news,
            'keywords' => $keywords
        ]);
    }

    public function cap_nhat() {
        $news = Post::where('category_id', CAPNHAT)->where('active', ACTIVE)->orderByDesc('created_at')->paginate(8);
        $keywords = $this->keywords();
        return view('tin-tuc.cap-nhat', [
            'posts' => $news,
            'keywords' => $keywords
        ]);
    }

    public function bao_tri() {
        $news = Post::where('category_id', BAOTRI)->where('active', ACTIVE)->orderByDesc('created_at')->paginate(8);
        $keywords = $this->keywords();
        return view('tin-tuc.bao-tri', [
            'posts' => $news,
            'keywords' => $keywords
        ]);
    }

    public function tin_tuc_detail($post) {
        $detail = Post::where('title_domain', 'LIKE', $post)
            ->first();
        dd($detail);
        $news = Post::where('category_id', TINTUC)->where('active', ACTIVE)->orderByDesc('created_at')->limit(5)->get();
        $keywords = $this->keywords();
        return view('chi-tiet', [
            'post' => $detail,
            'news' => $news,
            'keywords' => $keywords
        ]);
    }

    public function su_kien_detail($post) {
        $detail = Post::where('title_domain', 'LIKE', $post)
            ->first();
        $keywords = $this->keywords();
        return view('tin-tuc.detail', [
            'post' => $detail,
            'keywords' => $keywords
        ]);
    }

    public function cap_nhat_detail($post) {
        $detail = Post::where('title_domain', 'LIKE', $post)
            ->first();
        $keywords = $this->keywords();
        return view('tin-tuc.detail', [
            'post' => $detail,
            'keywords' => $keywords
        ]);
    }

    public function bao_tri_detail($post) {
        $detail = Post::where('title_domain', 'LIKE', $post)
            ->first();
        $keywords = $this->keywords();
        return view('tin-tuc.detail', [
            'post' => $detail,
            'keywords' => $keywords
        ]);
    }
    public function tin_tuc_pagination(Request $request) {
        $news = Post::where('category_id', TINTUC)->where('active', ACTIVE)->orderByDesc('created_at')->paginate(5);
        return view('layout.homepage.element.tin-tuc', [
            'news' => $news,
        ]);
    }

    public function clip_pagination(Request $request)
    {
        $clips = Clip::where('active', ACTIVE)->orderByDesc('created_at')->paginate(5);
        return view('layout.homepage.element.clip', [
            'clips' => $clips,
        ]);
    }
}
