<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\ActorSlide;
use App\Models\BankRanking;
use App\Models\Banner;
use App\Models\Category;
use App\Models\Customer;
use App\Models\General;
use App\Models\KeyWord;
use App\Models\Post;
use App\Models\Ranking;
use App\Models\Register;
use App\Models\ScoinUser;
use App\Models\Server;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class IndexController extends Controller
{
    public function index(Request $request) {
        $nguy = General::where('type', NGUY)->get();
        $thuc = General::where('type', THUC)->get();
        $ngo = General::where('type', NGO)->get();
        $quan = General::where('type', QUAN)->get();
        $keywords = $this->keywords();
        return view('trang-chu', [
            'keywords' => $keywords,
            'nguy' => $nguy,
            'thuc' => $thuc,
            'ngo' => $ngo,
            'quan' => $quan
        ]);
    }

    public function general_detail($detail)
    {
        $detail = General::where('domain_name', $detail)->first();

        return view('chi-tiet-tuong', [
            'detail' => $detail
        ]);
    }

    public function gioi_thieu(Request $request) {
        $keywords = $this->keywords();
        return view('gioi-thieu', [
            'keywords' => $keywords
        ]);
    }
    public function tai_game(Request $request) {
        $keywords = [];
        return view('tai-game.main', [
            'keywords' => $keywords
        ]);
    }

    public function rankPaginate(Request $request) {
        $rank = $this->getRanking(0);
        $servers = Server::all();
        return view('layout.homepage.index.bxh', [
            'ranks' => $rank,
            'servers' => $servers
        ]);
    }

    public function rankFilter(Request $request) {
        $servers = Server::all();
        $filter = [];
        $nameCache = 'page=' . $request->page;
        $type = null;
        if (isset($request->server_id)) {
            $filter['server_id'] = $request->server_id;
            $type = '&server_id=' . $request->server_id;
            $nameCache = $nameCache . 'server_id='.$request->server_id.'-';
        }
        if (isset($request->faction)) {
            $filter['role_faction'] = $request->faction;
            $type = '&faction=' . $request->faction;
            $nameCache = $nameCache . 'role_faction='.$request->faction.'-';
        }
        if (isset($request->class)) {
            $filter['role_class'] = $request->class;
            $type = '&class=' . $request->class;
            $nameCache = $nameCache . 'role_class='.$request->class;
        }
        $rank = Cache::remember($nameCache, 720 * 60, function () use ($filter) {
            return Ranking::query()
                ->with('server:id,name')
                ->where($filter)
                ->whereHas('server')
                ->orderByDesc('role_level')
                ->paginate(10);
        });
        return view('layout.homepage.index.bxh', [
            'ranks' => $rank,
            'servers' => $servers,
            'type' => $type
        ]);
    }

    public function indexTinTuc(Request $request) {
        $posts = Post::with('category')
            ->where('category_id', TINTUC)
            ->where('active', ACTIVE)
            ->orderByDesc('created_at')->paginate(5);
        return view('trang-chu.tintuc', [
            'tintuc' => $posts,
        ]);
    }

    public function indexSukien(Request $request) {
        $posts = Post::with('category')
            ->where('category_id', SUKIEN)
            ->where('active', ACTIVE)
            ->orderByDesc('created_at')->paginate(5);
        return view('trang-chu.sukien', [
            'sukien' => $posts,
        ]);
    }

    public function indexBaotri(Request $request) {
        $posts = Post::with('category')
            ->where('category_id', BAOTRI)
            ->where('active', ACTIVE)
            ->orderByDesc('created_at')->paginate(5);
        return view('trang-chu.baotri', [
            'baotri' => $posts,
        ]);
    }

    public function search(Request $request) {
        if (!empty($request->search)) {
            $news = Post::where('active', ACTIVE)
                ->where('title', 'LIKE', '%'. $request->search .'%')
                ->orderByDesc('created_at')->paginate(8);
        } else {
            $news = Post::where('active', ACTIVE)
                ->orderByDesc('created_at')->paginate(8);
        }
        return view('search', [
            'news' => $news,
        ]);
    }

    public function search_detail($post) {
        $detail = Post::with('category')
            ->where('active', ACTIVE)
            ->where('title_domain', 'LIKE', $post)
            ->first();
        return view('search-detail', [
            'post' => $detail
        ]);
    }


    public function ranking($serverId): \Illuminate\Http\JsonResponse
    {
        $serverId = (int) $serverId;
        $rank = $this->getRanking($serverId);

        return response()->json([
            'status' => 1,
            'data' => $rank
        ]);
    }

    protected function getRanking($serverId)
    {
        return Cache::remember('ranking-index', 60 * 60, function () use ($serverId) {
            return Ranking::query()
                ->with('server:id,name')
                ->whereHas('server')
                ->orderBy('index', 'asc')
                ->paginate(10);
        });
    }

    public function newPage(Request $request) {
        $page = !empty($request->page) ? $request->page : 1;
        $news = Post::where('type', NEWS_POST)->where('active', ACTIVE)->orderBy('created_at', 'desc')->paginate(10);
        return view('news', [
            'page' => $page,
            'news' => $news,
            'type' => NEWS_POST,
            'routeName' => 'index.new-page',
        ]);
    }

    public function eventPage() {
        $page = !empty($request->page) ? $request->page : 1;
        $news = Post::where('type', EVENT_POST)->where('active', ACTIVE)->orderBy('created_at', 'desc')->paginate(10);
        return view('news', [
            'page' => $page,
            'news' => $news,
            'type' => EVENT_POST,
            'routeName' => 'index.event-page',
        ]);
    }

    public function instructionPage(Request $request) {
        $page = !empty($request->page) ? $request->page : 1;
        $news = Post::where('type', INSTRUCTION_POST)->where('active', ACTIVE)->orderBy('created_at', 'desc')->paginate(10);
        return view('news', [
            'page' => $page,
            'news' => $news,
            'type' => INSTRUCTION_POST,
            'routeName' => 'index.instruction-page',
        ]);
    }

    public function postPage($id) {
        $post = Post::find($id);
        $news = Post::where('type', NEWS_POST)->where('active', ACTIVE)->orderBy('created_at', 'desc')->paginate(5);
        return view('new-details', [
            'post' => $post,
            'news' => $news
        ]);
    }

    public function download() {
        return view('download');
    }

    public function check_dang_ky(Request $request) {
        $exists = Customer::where('email', $request->email)
            ->orWhere('phone', $request->phone)
            ->first();
       if (!empty($exists)) {
           if ($exists->email == $request->email) {
               return ['code' => -1, 'message' => 'Email này đã được sử dụng. Vui lòng nhập email khác.'];
           }
           if ($exists->phone == $request->phone) {
               return ['code' => -1, 'message' => 'Số điện thoại này đã được sử dụng. Vui lòng nhập số điện thoại khác.'];
           }
       } else {
            $customer = Customer::create($request->all());
            return ['code' => 1];
        }
    }

    public function register(Request $request) {
        $checkData = $request->session()->get('scoinLogin');
        $check = false;
        if (!empty($checkData)) {
            $request->session()->forget('scoinLogin');
            if ($checkData == 1) {
                $check = 1;
            } else {
                $check = 2;
            }
        }
        $count = Register::count();
        $date1 = Carbon::now(); // ví dụ: 2025-05-26
        $date2 = Carbon::parse('2025-06-11');

        $diffInDays = $date1->diffInDays($date2); // Kết quả: 16
        $countDay = (16 - $diffInDays) > 0 ? (16 - $diffInDays) * 2686 : 2686;
        $count = $count * 7 + 565 + $countDay;
        $array = str_split((string)$count);
        $array = array_map('intval', $array);
        return view('register', [
            'count' => $count,
            'count_array' => $array,
            'check' => $check
        ]);
    }

    public function login(Request $request) {
        $data = $this->getToken($request->code);
        $check = $this->getScoinUser($data);
        $request->session()->put('scoinLogin', $check);
        $redirectUri = route('register.logout'); // URL sẽ quay lại sau khi đăng nhập hoặc logout
        $oauthUrl = 'https://graph.vtcmobile.vn/oauth/authorize?' . http_build_query([
                'client_id' => '5841b5a8bcb690e2664b9ef070d4c235',
                'redirect_uri' => $redirectUri,
                'agencyid' => 1,
                'action' => 'logout'
            ]);
        return redirect($oauthUrl);
    }

    public function logout(Request $request) {
        return view('layout.homepage.closepopup');
    }

    public function getToken($code) {
        $url = config('base.urlBaseToken') . "?client_id=" . config('base.client_id') . "&client_secret=" . config('base.client_secret') . "&redirect_uri=" . config('base.redirect_url') . "&code=" . $code;
        $response = Http::withoutVerifying()->get($url);
        return json_decode($response->body(), true);
    }

    public function getScoinUser($userLogin) {
        $user = Register::where('scoin_id', $userLogin['UserId'])->first();
        if (empty($user)) {
            $user = new Register;
            $user->scoin_id = $userLogin['UserId'];
            $user->scoin_name = $userLogin['User'];
            $user->save();
            return 1;
        }
        return 2;
    }
}
